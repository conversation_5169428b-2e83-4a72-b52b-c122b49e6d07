const { chromium } = require('playwright');
const fs = require('fs');

async function simpleScrap() {
    console.log('启动浏览器...');
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        const url = 'https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN?product_id=course_2wz9KwPb5MUq4W63laK2HNYGSDU&task_id=pt_68215ca6e4cfe_kd4hq1pC';
        
        console.log('正在访问目标网页...');
        await page.goto(url, {
            waitUntil: 'networkidle',
            timeout: 60000
        });
        
        console.log('等待页面加载...');
        await page.waitForTimeout(10000);
        
        // 获取页面标题
        const title = await page.title();
        console.log('页面标题:', title);
        
        // 获取页面URL
        const currentUrl = page.url();
        console.log('当前URL:', currentUrl);
        
        // 保存页面HTML
        const html = await page.content();
        fs.writeFileSync('page_source.html', html, 'utf8');
        console.log('页面HTML已保存到 page_source.html');
        
        // 保存页面截图
        await page.screenshot({ 
            path: 'page_screenshot.png', 
            fullPage: true 
        });
        console.log('页面截图已保存到 page_screenshot.png');
        
        // 尝试获取页面中的所有文本
        const allText = await page.evaluate(() => {
            return document.body.innerText;
        });
        
        fs.writeFileSync('page_text.txt', allText, 'utf8');
        console.log('页面文本已保存到 page_text.txt');
        
        // 尝试查找可能的题目元素
        console.log('正在搜索可能的题目元素...');
        
        const possibleQuestions = await page.evaluate(() => {
            const results = [];
            const elements = document.querySelectorAll('*');
            
            elements.forEach((element, index) => {
                const text = element.textContent || '';
                const tagName = element.tagName.toLowerCase();
                const className = element.className || '';
                const id = element.id || '';
                
                // 查找可能包含题目的元素
                if (text.length > 30 && text.length < 2000) {
                    // 检查是否包含题目相关关键词
                    const keywords = ['题', '问', '选择', '判断', 'A.', 'B.', 'C.', 'D.', '正确', '错误'];
                    const hasKeyword = keywords.some(keyword => text.includes(keyword));
                    
                    if (hasKeyword) {
                        results.push({
                            index: index,
                            tagName: tagName,
                            className: className,
                            id: id,
                            textLength: text.length,
                            text: text.substring(0, 200) + (text.length > 200 ? '...' : '')
                        });
                    }
                }
            });
            
            return results;
        });
        
        console.log(`找到 ${possibleQuestions.length} 个可能的题目元素`);
        
        if (possibleQuestions.length > 0) {
            fs.writeFileSync('possible_questions.json', JSON.stringify(possibleQuestions, null, 2), 'utf8');
            console.log('可能的题目元素已保存到 possible_questions.json');
            
            // 创建可读格式
            let readableQuestions = '可能的题目元素\n';
            readableQuestions += '='.repeat(50) + '\n\n';
            
            possibleQuestions.forEach((q, index) => {
                readableQuestions += `元素 ${index + 1}:\n`;
                readableQuestions += `标签: ${q.tagName}\n`;
                readableQuestions += `类名: ${q.className}\n`;
                readableQuestions += `ID: ${q.id}\n`;
                readableQuestions += `文本长度: ${q.textLength}\n`;
                readableQuestions += `内容预览: ${q.text}\n`;
                readableQuestions += '-'.repeat(30) + '\n\n';
            });
            
            fs.writeFileSync('possible_questions.txt', readableQuestions, 'utf8');
            console.log('可读格式已保存到 possible_questions.txt');
        }
        
        // 检查是否有登录表单
        const loginElements = await page.evaluate(() => {
            const loginSelectors = [
                'input[type="password"]',
                'input[name*="password"]',
                'input[name*="login"]',
                'input[name*="username"]',
                '.login',
                '.signin',
                '[class*="login"]',
                '[class*="signin"]'
            ];
            
            const found = [];
            loginSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    found.push({
                        selector: selector,
                        count: elements.length
                    });
                }
            });
            
            return found;
        });
        
        if (loginElements.length > 0) {
            console.log('检测到登录相关元素:', loginElements);
            console.log('该页面可能需要登录才能查看完整内容');
        }
        
        console.log('爬取完成！请查看生成的文件了解页面内容。');
        
    } catch (error) {
        console.error('爬取过程中出现错误:', error);
    } finally {
        await browser.close();
    }
}

// 运行爬虫
simpleScrap().catch(console.error);
