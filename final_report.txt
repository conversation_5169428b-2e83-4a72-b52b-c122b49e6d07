CISP网络安全知识竞赛题目爬取最终报告
============================================================
生成时间: 2025/6/1 22:20:57
目标网址: https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN

爬取结果摘要:
------------------------------
总提取条目: 5
有效题目: 4
提取方法: selector, api

遇到的挑战:
------------------------------
1. 网站需要登录才能访问完整内容
2. 页面使用JavaScript动态加载内容
3. 大部分API接口需要身份验证
4. 题目内容分散在多个DOM元素中
5. 页面可能有反爬虫机制

改进建议:
------------------------------
1. 手动登录后再运行爬虫脚本
2. 使用浏览器扩展或用户脚本获取更多内容
3. 分析网站的API接口，直接调用数据接口
4. 使用更长的等待时间确保页面完全加载
5. 考虑使用代理或更换User-Agent避免检测

提取到的题目详情:
------------------------------

【题目 1】
题目: 81、下列不属于公钥密码的特点的是（）。A.公钥私钥成对出现B.加密密钥和解密密钥相同C.公钥加密私钥解密-机密性D.私钥加密公钥解密-数字签名 回答正确 正确答案： B 你的答案： B解析：公钥密码的加密密钥和解密密钥不同。
提取方法: selector
----------------------------------------

【题目 2】
题目: A.公钥私钥成对出现B.加密密钥和解密密钥相同C.公钥加密私钥解密-机密性D.私钥加密公钥解密-数字签名 回答正确 正确答案： B 你的答案： B解析：公钥密码的加密密钥和解密密钥不同。
提取方法: selector
----------------------------------------

【题目 3】
题目: 回答正确 正确答案： B 你的答案： B解析：公钥密码的加密密钥和解密密钥不同。
提取方法: selector
----------------------------------------

【题目 4】
题目: 练习任务-2025校园网络安全知识竞赛练习题
提取方法: api
----------------------------------------

【题目 5】
编号: 81
题目: 下列不属于公钥密码的特点的是（）。
选项:
  A. 公钥私钥成对出现
  B. 加密密钥和解密密钥相同
  C. 公钥加密私钥解密-机密性
  D. 私钥加密公钥解密-数字签名
正确答案: B
解析: 公钥密码的加密密钥和解密密钥不同。
提取方法: 未知
----------------------------------------

技术实现说明:
------------------------------
使用工具: Playwright (无头浏览器)
编程语言: Node.js
提取策略: CSS选择器 + 文本搜索 + API拦截
数据格式: JSON, CSV, TXT

文件说明:
------------------------------
final_report.json - 完整的JSON格式报告
final_report.txt - 本可读格式报告
final_questions.csv - Excel兼容的题目数据
parsed_questions.* - 解析后的题目数据
questions_detailed.json - 原始提取数据
page_screenshot.png - 页面截图
README.md - 使用说明

