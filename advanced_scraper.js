const { chromium } = require('playwright');
const fs = require('fs');

class CISPQuestionScraper {
    constructor() {
        this.browser = null;
        this.page = null;
        this.questions = [];
    }

    async init() {
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        
        // 设置用户代理
        await this.page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });

        // 设置视口
        await this.page.setViewportSize({ width: 1920, height: 1080 });
    }

    async navigateToPage() {
        const url = 'https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN?product_id=course_2wz9KwPb5MUq4W63laK2HNYGSDU&task_id=pt_68215ca6e4cfe_kd4hq1pC';
        
        console.log('正在访问目标网页...');
        await this.page.goto(url, {
            waitUntil: 'networkidle',
            timeout: 60000
        });
        
        // 等待页面加载
        await this.page.waitForTimeout(3000);
        
        // 检查是否需要登录
        const needsLogin = await this.checkIfLoginRequired();
        if (needsLogin) {
            console.log('检测到需要登录，请手动登录后按回车继续...');
            await this.waitForUserInput();
        }
    }

    async checkIfLoginRequired() {
        const loginIndicators = [
            'text=登录',
            'text=请登录',
            'input[type="password"]',
            '.login-form',
            '.login-container'
        ];
        
        for (const indicator of loginIndicators) {
            try {
                const element = await this.page.$(indicator);
                if (element) {
                    return true;
                }
            } catch (e) {
                // 继续检查下一个指示器
            }
        }
        return false;
    }

    async waitForUserInput() {
        return new Promise((resolve) => {
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            rl.question('按回车键继续...', () => {
                rl.close();
                resolve();
            });
        });
    }

    async extractQuestions() {
        console.log('开始提取题目...');
        
        // 等待内容加载
        await this.page.waitForTimeout(5000);
        
        // 尝试多种提取策略
        await this.tryExtractBySelectors();
        await this.tryExtractByText();
        await this.tryExtractByAPI();
        
        return this.questions;
    }

    async tryExtractBySelectors() {
        const selectors = [
            '.question-item',
            '.practice-question',
            '.exam-question',
            '.question-container',
            '.question-box',
            '.question',
            '.item',
            '[data-question]',
            '[class*="question"]',
            '[class*="题"]',
            '.list-item',
            '.content-item'
        ];

        for (const selector of selectors) {
            try {
                const elements = await this.page.$$(selector);
                if (elements.length > 0) {
                    console.log(`使用选择器 ${selector} 找到 ${elements.length} 个元素`);
                    
                    for (let i = 0; i < elements.length; i++) {
                        const element = elements[i];
                        const text = await element.textContent();
                        const html = await element.innerHTML();
                        
                        if (text && text.trim().length > 20) {
                            this.questions.push({
                                method: 'selector',
                                selector: selector,
                                index: i + 1,
                                text: text.trim(),
                                html: html
                            });
                        }
                    }
                    
                    if (this.questions.length > 0) {
                        console.log(`通过选择器提取到 ${this.questions.length} 个题目`);
                        return;
                    }
                }
            } catch (e) {
                console.log(`选择器 ${selector} 失败:`, e.message);
            }
        }
    }

    async tryExtractByText() {
        if (this.questions.length > 0) return;
        
        console.log('尝试通过文本内容提取...');
        
        const textContent = await this.page.evaluate(() => {
            const results = [];
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            let node;
            while (node = walker.nextNode()) {
                const text = node.textContent.trim();
                if (text.length > 20 && 
                    (text.includes('题') || text.includes('问') || 
                     text.includes('选择') || text.includes('判断') ||
                     text.includes('A.') || text.includes('B.'))) {
                    results.push({
                        text: text,
                        parentTag: node.parentElement.tagName,
                        parentClass: node.parentElement.className
                    });
                }
            }
            
            return results;
        });
        
        if (textContent.length > 0) {
            console.log(`通过文本搜索找到 ${textContent.length} 个可能的题目`);
            this.questions = this.questions.concat(textContent.map((item, index) => ({
                method: 'text_search',
                index: index + 1,
                ...item
            })));
        }
    }

    async tryExtractByAPI() {
        if (this.questions.length > 0) return;
        
        console.log('尝试拦截API请求...');
        
        // 监听网络请求
        this.page.on('response', async (response) => {
            const url = response.url();
            if (url.includes('api') || url.includes('question') || url.includes('practice')) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        console.log('发现API响应:', url);
                        
                        // 保存API响应数据
                        fs.writeFileSync(`api_response_${Date.now()}.json`, JSON.stringify(data, null, 2));
                        
                        // 尝试从API数据中提取题目
                        this.extractFromAPIData(data);
                    }
                } catch (e) {
                    console.log('解析API响应失败:', e.message);
                }
            }
        });
        
        // 刷新页面以触发API请求
        await this.page.reload({ waitUntil: 'networkidle' });
        await this.page.waitForTimeout(5000);
    }

    extractFromAPIData(data) {
        // 递归搜索API数据中的题目信息
        const searchForQuestions = (obj, path = '') => {
            if (typeof obj !== 'object' || obj === null) return;
            
            for (const [key, value] of Object.entries(obj)) {
                const currentPath = path ? `${path}.${key}` : key;
                
                if (typeof value === 'string' && value.length > 20 &&
                    (value.includes('题') || value.includes('问') || 
                     value.includes('选择') || value.includes('判断'))) {
                    
                    this.questions.push({
                        method: 'api',
                        path: currentPath,
                        text: value
                    });
                } else if (Array.isArray(value)) {
                    value.forEach((item, index) => {
                        searchForQuestions(item, `${currentPath}[${index}]`);
                    });
                } else if (typeof value === 'object') {
                    searchForQuestions(value, currentPath);
                }
            }
        };
        
        searchForQuestions(data);
    }

    async saveResults() {
        if (this.questions.length === 0) {
            console.log('未找到题目，保存页面信息用于调试...');
            
            // 保存页面HTML
            const html = await this.page.content();
            fs.writeFileSync('debug_page.html', html, 'utf8');
            
            // 保存页面截图
            await this.page.screenshot({ path: 'debug_screenshot.png', fullPage: true });
            
            console.log('调试信息已保存到 debug_page.html 和 debug_screenshot.png');
            return;
        }
        
        // 去重和清理
        const uniqueQuestions = this.removeDuplicates(this.questions);
        
        const result = {
            url: this.page.url(),
            timestamp: new Date().toISOString(),
            totalQuestions: uniqueQuestions.length,
            extractionMethods: [...new Set(uniqueQuestions.map(q => q.method))],
            questions: uniqueQuestions
        };
        
        // 保存JSON格式
        fs.writeFileSync('questions_detailed.json', JSON.stringify(result, null, 2), 'utf8');
        
        // 保存可读格式
        let readableContent = `CISP网络安全知识竞赛练习题\n`;
        readableContent += `=`.repeat(50) + '\n';
        readableContent += `提取时间: ${new Date().toLocaleString()}\n`;
        readableContent += `题目总数: ${uniqueQuestions.length}\n`;
        readableContent += `提取方法: ${result.extractionMethods.join(', ')}\n\n`;
        
        uniqueQuestions.forEach((q, index) => {
            readableContent += `题目 ${index + 1} (${q.method})\n`;
            readableContent += `-`.repeat(30) + '\n';
            readableContent += `${q.text}\n\n`;
        });
        
        fs.writeFileSync('questions_readable.txt', readableContent, 'utf8');
        
        console.log(`成功提取 ${uniqueQuestions.length} 个题目`);
        console.log('详细数据已保存到 questions_detailed.json');
        console.log('可读格式已保存到 questions_readable.txt');
    }

    removeDuplicates(questions) {
        const seen = new Set();
        return questions.filter(q => {
            const key = q.text.substring(0, 100); // 使用前100个字符作为去重键
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.init();
            await this.navigateToPage();
            await this.extractQuestions();
            await this.saveResults();
        } catch (error) {
            console.error('爬取过程中出现错误:', error);
        } finally {
            await this.close();
        }
    }
}

// 运行爬虫
const scraper = new CISPQuestionScraper();
scraper.run().catch(console.error);
