// ==UserScript==
// @name         CISP 自动答题助手 (最终版)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  智能遍历所有题目，识别已答/未答状态，自动答题并导出完整记录
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let currentQuestionIndex = 0;
  let totalQuestions = 0;
  let speedMultiplier = 1; // 倍速控制

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms / speedMultiplier));
  }

  // 页面加载检测函数 - 高倍速优化版
  async function waitForPageLoad(maxWaitTime = 5000) {
    const startTime = Date.now();
    const adjustedMaxWait = Math.max(1000, maxWaitTime / speedMultiplier); // 高倍速时减少最大等待时间

    while (Date.now() - startTime < adjustedMaxWait) {
      // 检查页面基本元素是否加载完成
      const hasQuestion = document.querySelector('.questionStem') ||
                         document.querySelector('.question-content') ||
                         document.querySelector('[class*="question"]');

      const hasOptions = document.querySelectorAll('.answerItem').length >= 2;

      if (hasQuestion && hasOptions) {
        console.log('✅ 页面基本元素加载完成');
        return true;
      }

      // 高倍速时减少检查频率，避免过度消耗资源
      const checkInterval = Math.max(50, 150 / speedMultiplier);
      await delay(checkInterval);
    }

    // 即使超时，如果有基本元素也认为可以继续
    const hasBasicElements = document.querySelector('.questionStem, .question-content, [class*="question"]') &&
                            document.querySelectorAll('.answerItem').length >= 1;

    if (hasBasicElements) {
      console.log('⚠️ 页面加载超时，但检测到基本元素，继续处理');
      return true;
    }

    console.log('❌ 页面加载失败，未检测到基本元素');
    return false;
  }

  // 等待答题信息完全读取 - 高倍速优化版
  async function waitForAnswerInfoLoad(maxWaitTime = 4000) {
    const startTime = Date.now();
    const adjustedMaxWait = Math.max(800, maxWaitTime / speedMultiplier); // 高倍速时大幅减少等待时间
    let lastCorrectAnswer = '';
    let lastExplanation = '';
    let stableCount = 0;
    const requiredStableCount = speedMultiplier >= 10 ? 2 : 3; // 高倍速时减少稳定性要求

    while (Date.now() - startTime < adjustedMaxWait) {
      const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();

      // 高倍速时如果获取到任何信息就快速返回
      if (speedMultiplier >= 10 && (correctAnswer || explanation)) {
        console.log('✅ 高倍速模式：快速获取答题信息');
        return { correctAnswer, explanation };
      }

      // 检查信息是否稳定
      if (correctAnswer === lastCorrectAnswer && explanation === lastExplanation) {
        stableCount++;
        if (stableCount >= requiredStableCount) {
          console.log('✅ 答题信息读取完成');
          return { correctAnswer, explanation };
        }
      } else {
        stableCount = 0;
        lastCorrectAnswer = correctAnswer;
        lastExplanation = explanation;
      }

      // 高倍速时减少检查频率
      const checkInterval = Math.max(50, 200 / speedMultiplier);
      await delay(checkInterval);
    }

    console.log('⚠️ 答题信息读取超时，使用当前获取的信息');
    return { correctAnswer: lastCorrectAnswer, explanation: lastExplanation };
  }

  // 检查当前题目是否处理完成，可以切换到下一题 - 高倍速优化版
  async function isQuestionProcessingComplete(questionData, maxWaitTime = 2000) {
    const startTime = Date.now();
    const adjustedMaxWait = Math.max(300, maxWaitTime / speedMultiplier); // 高倍速时大幅减少等待时间

    // 高倍速模式下简化检查
    if (speedMultiplier >= 10) {
      console.log('✅ 高倍速模式：跳过详细处理完成检查');
      return true;
    }

    while (Date.now() - startTime < adjustedMaxWait) {
      // 检查基本数据是否获取完成
      const hasQuestionText = questionData.qText && questionData.qText.length > 10;
      const hasOptions = questionData.options && questionData.options.length >= 2;

      if (!hasQuestionText || !hasOptions) {
        console.log('⏳ 等待题目数据完整...');
        await delay(Math.max(50, 150 / speedMultiplier));
        continue;
      }

      // 检查是否有答案信息（正确答案或解析至少有一个）
      const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();
      const hasAnswerInfo = correctAnswer || (explanation && explanation.length > 10);

      // 检查用户答案状态
      const userAnswer = getUserAnswer();
      const hasUserAnswer = userAnswer && userAnswer !== '未获取到';

      // 如果有答案信息或用户已选择答案，认为处理完成
      if (hasAnswerInfo || hasUserAnswer) {
        console.log('✅ 题目处理完成，可以切换下一题');
        return true;
      }

      // 高倍速时减少检查频率
      const checkInterval = Math.max(50, 200 / speedMultiplier);
      await delay(checkInterval);
    }

    console.log('⚠️ 题目处理超时，强制切换下一题');
    return true; // 超时也允许切换，避免卡死
  }

  // 检测网络状态和页面响应
  async function checkNetworkAndPageResponse() {
    try {
      // 检查网络连接
      if (!navigator.onLine) {
        console.log('⚠️ 网络连接断开');
        return false;
      }

      // 检查页面是否响应
      const startTime = Date.now();
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);
      document.body.removeChild(testElement);
      const responseTime = Date.now() - startTime;

      if (responseTime > 1000) {
        console.log('⚠️ 页面响应缓慢');
        return false;
      }

      return true;
    } catch (error) {
      console.log('⚠️ 页面响应检测失败:', error);
      return false;
    }
  }

  // 智能等待函数 - 根据网络状态调整等待时间，高倍速优化
  async function smartDelay(baseMs) {
    // 高倍速时跳过网络检测，直接使用基础延时
    if (speedMultiplier >= 10) {
      await delay(baseMs);
      return;
    }

    const networkOk = await checkNetworkAndPageResponse();

    if (!networkOk) {
      // 网络不稳定时增加等待时间，但考虑倍速
      const extendedDelay = Math.max(baseMs * 1.5, 500 / speedMultiplier);
      console.log(`🌐 网络不稳定，延长等待时间至 ${extendedDelay}ms`);
      await delay(extendedDelay);
    } else {
      await delay(baseMs);
    }
  }

  function createFloatingUI() {
    const panel = document.createElement('div');
    panel.id = 'auto-answer-ui';
    panel.style.cssText = `
      position: fixed;
      top: 100px;
      right: 30px;
      background: #fff;
      border: 2px solid #4CAF50;
      border-radius: 10px;
      padding: 15px;
      z-index: 9999;
      box-shadow: 0 0 15px rgba(41, 24, 24, 0.3);
      min-width: 250px;
      font-family: Arial, sans-serif;
    `;
    panel.innerHTML = `
      <div style="font-weight:bold;color:#2E7D32;margin-bottom:10px">🤖 CISP自动答题助手</div>
      <button id="startAutoAnswer" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px">开始自动答题</button>
      <button id="stopAutoAnswer" style="width:100%;padding:8px;background:#f44336;color:white;border:none;border-radius:5px;cursor:pointer;display:none">停止答题</button>
      <div style="margin-bottom:10px">
        <label style="font-size:12px;color:#666">倍速控制：</label>
        <select id="speedControl" style="width:60px;padding:2px;margin-left:5px">
          <option value="1">1x</option>
          <option value="2">2x</option>
          <option value="5">5x</option>
          <option value="10">10x</option>
          <option value="20">20x</option>
        </select>
      </div>
      <button id="debugMode" style="width:100%;padding:6px;background:#2196F3;color:white;border:none;border-radius:5px;cursor:pointer;font-size:12px;margin-bottom:10px">调试模式</button>
      <div id="answer-status" style="color:#555;font-size:14px;margin-bottom:5px">状态：未启动</div>
      <div id="progress-info" style="color:#666;font-size:12px;margin-bottom:5px">进度：0/0</div>
      <div id="current-question" style="color:#333;font-size:11px;background:#f5f5f5;padding:5px;border-radius:3px">当前：未开始</div>
      <div id="network-status" style="color:#888;font-size:10px;margin-top:5px">网络：检测中</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startAutoAnswer').onclick = startAnswering;
    document.getElementById('stopAutoAnswer').onclick = stopAnswering;
    document.getElementById('debugMode').onclick = toggleDebugMode;
    document.getElementById('speedControl').onchange = function() {
      speedMultiplier = parseInt(this.value);
      console.log(`倍速设置为: ${speedMultiplier}x`);

      // 更新UI提示
      const networkElement = document.getElementById('network-status');
      if (networkElement) {
        if (speedMultiplier >= 10) {
          networkElement.innerText = `倍速：${speedMultiplier}x (高速模式)`;
          networkElement.style.color = '#FF9800';
        } else {
          networkElement.innerText = `倍速：${speedMultiplier}x (标准模式)`;
          networkElement.style.color = '#4CAF50';
        }
      }
    };
  }

  function startAnswering() {
    const ok = confirm("🚀 确认开始自动答题？\n\n✅ 系统将自动遍历所有题目\n✅ 识别已答/未答状态\n✅ 随机选择答案（未答题目）\n✅ 记录所有题目和解析\n✅ 完成后自动导出记录\n\n⚠️ 请确保网络稳定，过程中请勿操作页面");
    if (ok) {
      document.getElementById('startAutoAnswer').style.display = 'none';
      document.getElementById('stopAutoAnswer').style.display = 'block';
      updateUI('状态：初始化中...', '0/0', '正在分析页面结构...');
      startAutoAnswering();
    }
  }

  function stopAnswering() {
    answering = false;
    updateUI('状态：已停止', `${records.length}/${totalQuestions}`, '用户手动停止');
    document.getElementById('startAutoAnswer').style.display = 'block';
    document.getElementById('stopAutoAnswer').style.display = 'none';

    // 确保只要有记录就导出，哪怕只有一道题
    if (records.length > 0) {
      const shouldExport = confirm(`已停止答题，共处理了 ${records.length} 道题目。\n\n是否导出答题记录？\n\n记录包含：\n• 题目信息\n• 答案信息\n• 正确答案\n• 解析内容`);
      if (shouldExport) {
        exportTxt();
      }
    } else {
      // 即使没有完整记录，也尝试导出当前页面信息
      const currentData = getCurrentQuestionData();
      if (currentData.qText && currentData.options.length > 0) {
        const shouldExportCurrent = confirm('虽然没有完整的答题记录，但检测到当前页面有题目信息。\n\n是否导出当前题目信息？');
        if (shouldExportCurrent) {
          // 创建当前题目的记录
          const currentRecord = {
            index: 1,
            question: currentData.qText,
            options: currentData.options,
            yourAnswer: getUserAnswer() || '未获取到',
            correctAnswer: '未知',
            explanation: '停止时获取的当前题目信息',
            wasAnswered: false,
            answerAttempted: false,
            timestamp: new Date().toLocaleString()
          };
          records = [currentRecord];
          exportTxt();
        }
      } else {
        alert('没有检测到任何题目信息，无法导出记录。');
      }
    }
  }

  let debugMode = false;
  function toggleDebugMode() {
    debugMode = !debugMode;
    const btn = document.getElementById('debugMode');
    btn.textContent = debugMode ? '关闭调试' : '调试模式';
    btn.style.background = debugMode ? '#FF9800' : '#2196F3';
    
    if (debugMode) {
      analyzePageStructure();
    }
  }

  function analyzePageStructure() {
    console.log('\n=== 🔍 CISP页面结构分析 ===');

    // 1. 分析题目按钮
    console.log('\n📋 题目按钮分析:');
    const buttons = getQuestionList();
    console.log(`找到 ${buttons.length} 个题目按钮`);
    if (buttons.length > 0) {
      const firstBtn = buttons[0];
      console.log(`第一个按钮: "${firstBtn.textContent.trim()}"`);
      console.log(`按钮类名: ${firstBtn.className}`);
      console.log(`是否已答: ${isQuestionAnswered(firstBtn)}`);
    }

    // 2. 分析当前题目
    console.log('\n📝 当前题目分析:');
    const { qText, options } = getCurrentQuestionData();
    console.log(`题目: ${qText.substring(0, 100)}...`);
    console.log(`选项数量: ${options.length}`);
    options.forEach((opt, i) => {
      console.log(`  ${String.fromCharCode(65 + i)}. ${opt.substring(0, 50)}...`);
    });

    // 3. 分析选项元素
    console.log('\n🎯 选项元素分析:');
    const optionElements = document.querySelectorAll('.answerItem');
    console.log(`找到 ${optionElements.length} 个 .answerItem 元素`);
    optionElements.forEach((el, i) => {
      const style = window.getComputedStyle(el);
      console.log(`选项 ${i + 1}: ${el.textContent.trim().substring(0, 30)}...`);
      console.log(`  类名: ${el.className}`);
      console.log(`  背景: ${style.backgroundColor}`);
      console.log(`  文字: ${style.color}`);
    });

    // 4. 分析用户答案
    console.log('\n💡 用户答案分析:');
    const userAnswer = getUserAnswer();
    console.log(`当前答案: ${userAnswer || '未选择'}`);

    // 5. 分析正确答案和解析
    console.log('\n✅ 正确答案和解析分析:');
    const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();
    console.log(`正确答案: ${correctAnswer || '未找到'}`);
    console.log(`解析: ${explanation ? explanation.substring(0, 100) + '...' : '未找到'}`);

    // 6. 分析页面加载状态
    console.log('\n🌐 页面加载状态分析:');
    console.log(`页面就绪状态: ${document.readyState}`);
    console.log(`网络连接状态: ${navigator.onLine ? '在线' : '离线'}`);

    const loadingElements = document.querySelectorAll('.loading, .spinner, [class*="loading"]');
    console.log(`加载指示器数量: ${loadingElements.length}`);

    const hasQuestion = document.querySelector('.questionStem') ||
                       document.querySelector('.question-content') ||
                       document.querySelector('[class*="question"]');
    const hasOptions = document.querySelectorAll('.answerItem').length >= 2;
    console.log(`页面基本元素: 题目${hasQuestion ? '✓' : '✗'} 选项${hasOptions ? '✓' : '✗'}`);

    console.log('\n=== 🔍 分析完成 ===\n');

    updateUI('🔍 调试分析', '分析完成', '请查看控制台输出');

    alert('🔍 页面结构分析完成！\n\n请打开浏览器控制台（F12）查看详细分析结果。\n\n分析内容包括：\n• 题目按钮识别\n• 当前题目内容\n• 选项元素结构\n• 答案获取状态\n• 正确答案和解析\n• 页面加载状态\n• 网络连接状态');
  }

  // 智能获取题目按钮列表 - 针对脚本猫和CISP页面优化
  function getQuestionList() {
    let questionButtons = [];

    // 根据截图，左侧题目按钮的多种查找策略
    const strategies = [
      // 策略1: 查找答题卡区域的按钮（根据截图结构）
      () => {
        // 查找答题卡容器
        const answerCard = document.querySelector('.answer-card, .question-card, [class*="答题卡"]');
        if (answerCard) {
          const buttons = answerCard.querySelectorAll('button, div[onclick], span[onclick]');
          return Array.from(buttons).filter(btn => {
            const text = btn.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0;
          });
        }
        return [];
      },

      // 策略2: 直接查找所有数字按钮/元素
      () => {
        const allClickables = document.querySelectorAll('button, div[onclick], span[onclick], [role="button"], .clickable');
        return Array.from(allClickables).filter(el => {
          const text = el.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0 && parseInt(text) <= 2000;
        });
      },

      // 策略3: 查找左侧面板中的数字元素
      () => {
        const leftPanels = document.querySelectorAll('.left-panel, .sidebar, .question-nav, [class*="left"], [class*="side"]');
        for (const panel of leftPanels) {
          const elements = panel.querySelectorAll('*');
          const numElements = Array.from(elements).filter(el => {
            const text = el.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0 && el.offsetWidth > 0 && el.offsetHeight > 0;
          });
          if (numElements.length > 10) return numElements;
        }
        return [];
      },

      // 策略4: 通过父容器特征查找
      () => {
        // 查找包含多个数字的容器
        const containers = document.querySelectorAll('div, section, aside');
        for (const container of containers) {
          const children = container.children;
          const numChildren = Array.from(children).filter(child => {
            const text = child.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0;
          });
          if (numChildren.length >= 20) { // 假设至少20道题
            return numChildren;
          }
        }
        return [];
      }
    ];

    for (let i = 0; i < strategies.length; i++) {
      questionButtons = strategies[i]();
      if (questionButtons.length > 0) {
        console.log(`策略 ${i + 1} 找到 ${questionButtons.length} 个题目按钮`);
        break;
      }
    }

    // 如果还是没找到，尝试更宽泛的搜索
    if (questionButtons.length === 0) {
      console.log('尝试宽泛搜索...');
      const allElements = document.querySelectorAll('*');
      questionButtons = Array.from(allElements).filter(el => {
        const text = el.textContent.trim();
        const isNumber = /^\d+$/.test(text);
        const isVisible = el.offsetWidth > 0 && el.offsetHeight > 0;
        const isClickable = el.onclick || el.style.cursor === 'pointer' ||
                           el.tagName === 'BUTTON' || el.getAttribute('role') === 'button';
        return isNumber && isVisible && isClickable && parseInt(text) > 0 && parseInt(text) <= 100;
      });
    }

    // 按数字排序
    questionButtons.sort((a, b) => {
      const numA = parseInt(a.textContent.trim());
      const numB = parseInt(b.textContent.trim());
      return numA - numB;
    });

    console.log(`最终找到 ${questionButtons.length} 个题目按钮`);
    return questionButtons;
  }

  // 智能检测题目是否已答 - 根据截图优化
  function isQuestionAnswered(questionButton) {
    const text = questionButton.textContent.trim();
    const classList = questionButton.classList;
    const style = window.getComputedStyle(questionButton);

    // 检查类名 - 根据CISP网站可能的类名
    const answeredClasses = ['answered', 'completed', 'done', 'selected', 'active', 'finished', 'current'];
    const hasAnsweredClass = answeredClasses.some(cls => classList.contains(cls));

    // 检查样式 - 根据截图，已答题目可能有不同的背景色
    const bgColor = style.backgroundColor;
    const color = style.color;
    const borderColor = style.borderColor;

    // 默认未答状态的颜色
    const defaultBgColors = [
      'rgba(0, 0, 0, 0)',
      'transparent',
      'rgb(255, 255, 255)',
      'rgb(248, 249, 250)',
      'rgb(240, 240, 240)'
    ];

    const defaultTextColors = [
      'rgb(0, 0, 0)',
      'rgb(33, 37, 41)',
      'rgba(0, 0, 0, 0.87)',
      'rgb(51, 51, 51)'
    ];

    // 检查是否有特殊样式（通常表示已答）
    const hasSpecialBg = !defaultBgColors.includes(bgColor);
    const hasSpecialColor = !defaultTextColors.includes(color);
    const hasSpecialBorder = borderColor && borderColor !== 'rgb(0, 0, 0)' && borderColor !== 'rgba(0, 0, 0, 0)';

    // 检查是否有红色边框（根据截图，某些题目有红色边框）
    const hasRedBorder = borderColor && (
      borderColor.includes('rgb(255') ||
      borderColor.includes('red') ||
      borderColor.includes('#f') ||
      borderColor.includes('#e')
    );

    // 检查按钮是否被禁用（已答题目可能被禁用）
    const isDisabled = questionButton.disabled || questionButton.hasAttribute('disabled');

    const isAnswered = hasAnsweredClass || hasSpecialBg || hasSpecialColor || hasSpecialBorder || hasRedBorder || isDisabled;

    if (debugMode) {
      console.log(`题目 ${text}: ${isAnswered ? '已答' : '未答'}`);
      console.log(`  类名: ${classList.toString()}`);
      console.log(`  背景: ${bgColor}`);
      console.log(`  文字: ${color}`);
      console.log(`  边框: ${borderColor}`);
      console.log(`  禁用: ${isDisabled}`);
    }

    return isAnswered;
  }

  // 智能获取题目数据
  function getCurrentQuestionData() {
    let qText = '';
    let options = [];
    
    // 获取题目文本
    const questionSelectors = ['.questionStem', '.question-content', '.question-text', '[class*="question"]'];
    for (const selector of questionSelectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.trim().length > 10) {
        qText = element.innerText.trim();
        break;
      }
    }
    
    // 获取选项
    const optionSelectors = ['.answerItem', '.option-item', '.answer-option', '[class*="answer"]'];
    for (const selector of optionSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length >= 2) {
        options = Array.from(elements).map(el => el.innerText.trim()).filter(text => text.length > 0);
        break;
      }
    }
    
    return { qText, options };
  }

  // 智能获取用户答案 - 基于颜色识别优化
  function getUserAnswer() {
    const options = document.querySelectorAll('.answerItem');

    if (options.length === 0) {
      console.log('未找到选项元素');
      return '';
    }

    // 遍历所有选项，找到选中的（通过颜色识别）
    for (const option of options) {
      const style = window.getComputedStyle(option);
      const bgColor = style.backgroundColor;
      const textColor = style.color;

      // 检查是否为选中状态（根据截图，选中项会有特殊颜色）
      const isSelected = option.classList.contains('selected') ||
                        option.classList.contains('active') ||
                        // 检查背景色变化
                        (bgColor !== 'rgba(0, 0, 0, 0)' &&
                         bgColor !== 'transparent' &&
                         bgColor !== 'rgb(255, 255, 255)' &&
                         bgColor !== 'rgba(255, 255, 255, 1)') ||
                        // 检查文字颜色变化
                        (textColor !== 'rgb(0, 0, 0)' &&
                         textColor !== 'rgba(0, 0, 0, 1)');

      if (isSelected) {
        const answerText = option.textContent.trim();
        console.log(`🎯 通过颜色识别找到选中答案: ${answerText.substring(0, 30)}...`);
        console.log(`   背景色: ${bgColor}, 文字色: ${textColor}`);
        return answerText;
      }
    }

    console.log('⚠️ 未检测到选中的选项');
    return '';
  }

  // 智能获取正确答案和解析 - 基于页面读取优化
  function getCorrectAnswerAndExplanation() {
    let correctAnswer = '';
    let explanation = '';

    // 根据截图，寻找正确答案信息
    // 1. 查找页面中的"正确答案"文本
    const allText = document.body.innerText;
    const correctAnswerMatch = allText.match(/正确答案[：:]\s*([A-D])/i);
    if (correctAnswerMatch) {
      correctAnswer = correctAnswerMatch[1];
      console.log(`📝 找到正确答案: ${correctAnswer}`);
    }

    // 2. 查找解析内容
    const explanationMatch = allText.match(/解析[：:]?\s*([\s\S]*?)(?=\n\n|\n(?=[A-Z])|$)/i);
    if (explanationMatch) {
      explanation = explanationMatch[1].trim();
      console.log(`📖 找到解析内容: ${explanation.substring(0, 50)}...`);
    }

    // 3. 如果没找到，尝试其他方式
    if (!correctAnswer || !explanation) {
      // 查找包含"正确答案"或"解析"的元素
      const elements = document.querySelectorAll('*');
      for (const el of elements) {
        const text = el.textContent;

        if (!correctAnswer && text.includes('正确答案')) {
          const match = text.match(/正确答案[：:]\s*([A-D])/i);
          if (match) {
            correctAnswer = match[1];
          }
        }

        if (!explanation && text.includes('解析')) {
          const match = text.match(/解析[：:]?\s*(.*)/i);
          if (match && match[1].length > 10) {
            explanation = match[1].trim();
          }
        }

        if (correctAnswer && explanation) break;
      }
    }

    // 4. 尝试从特定的CSS选择器获取
    if (!correctAnswer) {
      const correctSelectors = ['.isCorrect', '.correct', '[class*="correct"]'];
      for (const selector of correctSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          const match = element.innerText.match(/[A-D]/);
          if (match) {
            correctAnswer = match[0];
            break;
          }
        }
      }
    }

    if (!explanation) {
      const explanationSelectors = ['.question-analysis', '.parse-info', '.explanation', '[class*="analysis"]'];
      for (const selector of explanationSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          let text = element.innerText.trim();
          text = text.replace(/^(解析|分析|说明)[:：]?\s*/, '');
          if (text.length > 10) {
            explanation = text;
            break;
          }
        }
      }
    }

    return { correctAnswer, explanation };
  }

  // 智能选择选项 - 根据页面元素优化
  async function selectRandomOption() {
    // 根据截图，选项是 .answerItem 元素
    const options = document.querySelectorAll('.answerItem');

    if (options.length === 0) {
      console.log('未找到 .answerItem 选项');
      return false;
    }

    console.log(`找到 ${options.length} 个选项`);

    // 检查是否已经选择（通过颜色或类名）
    for (const option of options) {
      const style = window.getComputedStyle(option);
      const bgColor = style.backgroundColor;
      const hasSelected = option.classList.contains('selected') ||
                         bgColor.includes('rgb(') && !bgColor.includes('255, 255, 255');

      if (hasSelected) {
        console.log('检测到已选择的选项');
        return true;
      }
    }

    // 随机选择一个选项
    const index = Math.floor(Math.random() * options.length);
    const selectedOption = options[index];

    console.log(`随机选择选项 ${String.fromCharCode(65 + index)}: ${selectedOption.textContent.trim().substring(0, 30)}...`);

    try {
      // 直接点击选项
      selectedOption.click();

      // 高倍速适配：减少等待时间
      await delay(Math.max(200, 1000 / speedMultiplier));

      // 验证选择结果
      const afterStyle = window.getComputedStyle(selectedOption);
      const afterBgColor = afterStyle.backgroundColor;
      const isSelected = selectedOption.classList.contains('selected') ||
                        afterBgColor !== 'rgba(0, 0, 0, 0)' &&
                        afterBgColor !== 'rgb(255, 255, 255)';

      if (isSelected) {
        console.log('✅ 选项选择成功');
        return true;
      } else {
        console.log('⚠️ 选项选择状态未确认，但已尝试点击');
        return true;
      }

    } catch (error) {
      console.error('❌ 选择选项时出错:', error);
      return false;
    }
  }

  function updateUI(status, progress, current) {
    document.getElementById('answer-status').innerText = status;
    document.getElementById('progress-info').innerText = progress;
    document.getElementById('current-question').innerText = current;
  }

  // 更新网络状态显示
  async function updateNetworkStatus() {
    const networkElement = document.getElementById('network-status');
    if (!networkElement) return;

    const networkOk = await checkNetworkAndPageResponse();
    if (networkOk) {
      networkElement.innerText = '网络：正常';
      networkElement.style.color = '#4CAF50';
    } else {
      networkElement.innerText = '网络：不稳定';
      networkElement.style.color = '#FF9800';
    }
  }

  // 定期检查网络状态
  setInterval(updateNetworkStatus, 5000);

  // 主要答题流程 - 优化已答题目处理
  async function startAutoAnswering() {
    answering = true;
    records = [];

    try {
      const questionList = getQuestionList();
      totalQuestions = questionList.length;

      if (totalQuestions === 0) {
        updateUI('❌ 错误：未找到题目', '0/0', '请检查页面或使用调试模式');
        return;
      }

      updateUI('🚀 开始答题', `0/${totalQuestions}`, `找到 ${totalQuestions} 道题目`);
      await delay(2000);

      for (let i = 0; i < questionList.length && answering; i++) {
        const questionButton = questionList[i];
        const questionNum = questionButton.textContent.trim();

        updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `正在处理题目 ${questionNum}`);

        try {
          // 点击题目
          questionButton.click();
          updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} - 加载页面`);

          // 等待页面完全加载
          const pageLoaded = await waitForPageLoad();
          if (!pageLoaded) {
            console.log(`题目 ${questionNum} 页面加载失败，跳过`);
            continue;
          }

          // 获取题目数据
          const { qText, options } = getCurrentQuestionData();
          if (!qText || options.length === 0) {
            console.log(`题目 ${questionNum} 数据获取失败，跳过`);
            continue;
          }

          // 检查是否已答（点击后再次检查，因为页面状态可能变化）
          const isAnswered = isQuestionAnswered(questionButton);
          let yourAnswer = '';
          let answerAttempted = false;

          // 先尝试获取已选答案
          yourAnswer = getUserAnswer();

          if (!yourAnswer && !isAnswered) {
            // 如果没有已选答案且题目未答，尝试随机选择
            updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} - 选择答案`);
            answerAttempted = await selectRandomOption();
            if (answerAttempted) {
              await delay(Math.max(300, 1000 / speedMultiplier)); // 高倍速适配
              yourAnswer = getUserAnswer();
            }
          }

          // 如果题目已答但无法获取答案，尝试从页面其他地方获取
          if (isAnswered && !yourAnswer) {
            // 查找可能显示答案的元素
            const answerElements = document.querySelectorAll('.your-answer, .selected-answer, .user-answer, [class*="answer"]');
            for (const el of answerElements) {
              const text = el.innerText.trim();
              if (text.match(/[A-D]/)) {
                yourAnswer = text;
                break;
              }
            }
          }

          // 等待答题信息完全加载
          updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} - 读取答题信息`);
          const { correctAnswer, explanation } = await waitForAnswerInfoLoad();

          // 记录数据
          const record = {
            index: parseInt(questionNum),
            question: qText,
            options,
            yourAnswer: yourAnswer || '未获取到',
            correctAnswer: correctAnswer || '未知',
            explanation: explanation || '暂无解析',
            wasAnswered: isAnswered,
            answerAttempted: answerAttempted,
            timestamp: new Date().toLocaleString()
          };

          records.push(record);

          if (debugMode) {
            console.log(`完成题目 ${questionNum}:`);
            console.log(`  状态: ${isAnswered ? '原已答' : '新答题'}`);
            console.log(`  你的答案: ${yourAnswer}`);
            console.log(`  正确答案: ${correctAnswer}`);
          }

          // 更新状态显示
          const statusText = isAnswered ? '(原已答)' : (answerAttempted ? '(新答题)' : '(仅记录)');
          updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} ${statusText} 完成`);

          // 确保当前题目处理完成后再切换下一题
          updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} - 确认处理完成`);
          await isQuestionProcessingComplete({ qText, options });

        } catch (error) {
          console.error(`处理题目 ${questionNum} 时出错:`, error);
          // 即使出错也要记录基本信息
          const { qText, options } = getCurrentQuestionData();
          if (qText) {
            records.push({
              index: parseInt(questionNum),
              question: qText,
              options,
              yourAnswer: '处理出错',
              correctAnswer: '未知',
              explanation: `处理出错: ${error.message}`,
              wasAnswered: false,
              answerAttempted: false,
              timestamp: new Date().toLocaleString()
            });
          }
        }

        // 使用智能等待，根据网络状态调整
        await smartDelay(Math.max(100, 500 / speedMultiplier));
      }

      updateUI('✅ 完成答题', `${records.length}/${totalQuestions}`, '正在导出记录...');
      await delay(1000);
      exportTxt();

    } catch (error) {
      console.error('答题流程出错:', error);
      updateUI('❌ 出现错误', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startAutoAnswer').style.display = 'block';
      document.getElementById('stopAutoAnswer').style.display = 'none';
    }
  }

  function exportTxt() {
    if (records.length === 0) {
      alert('没有记录可导出');
      return;
    }

    console.log(`开始导出 ${records.length} 条记录`);

    // 统计信息
    const totalCount = records.length;
    const wasAnsweredCount = records.filter(r => r.wasAnswered).length;
    const newAnsweredCount = records.filter(r => r.answerAttempted).length;
    const onlyRecordedCount = records.filter(r => !r.wasAnswered && !r.answerAttempted).length;
    const errorCount = records.filter(r => r.yourAnswer === '处理出错').length;
    const correctCount = records.filter(r => r.yourAnswer && r.correctAnswer &&
      r.yourAnswer.charAt(0) === r.correctAnswer).length;

    const header = `CISP自动答题记录 - 最终版本
${'='.repeat(80)}
📋 基本信息
生成时间：${new Date().toLocaleString()}
脚本版本：3.0 (最终优化版)
倍速设置：${speedMultiplier}x
网站地址：${window.location.href}

📊 统计信息
• 总题数：${totalCount} 道
• 原已答：${wasAnsweredCount} 道 (无需重新答题)
• 新答题：${newAnsweredCount} 道 (脚本自动答题)
• 仅记录：${onlyRecordedCount} 道 (无法答题但已记录)
• 处理错误：${errorCount} 道
• 答对题数：${correctCount} 道 (基于已知正确答案)

📈 准确率分析
${newAnsweredCount > 0 ? `新答题准确率：${((correctCount / Math.max(newAnsweredCount, 1)) * 100).toFixed(1)}%` : '无新答题'}
${totalCount > 0 ? `总体准确率：${((correctCount / totalCount) * 100).toFixed(1)}%` : ''}

${'='.repeat(80)}

`;

    // 筛选有价值的记录：正确答案记录题目+选项+正确答案，错误答案加上解析
    const validRecords = records.filter(rec => rec.question && rec.options && rec.options.length > 0);
    console.log(`有效记录数量: ${validRecords.length}/${records.length}`);

    if (validRecords.length === 0) {
      alert('没有有效的题目记录可导出');
      return;
    }

    const content = validRecords.map((rec) => {
        const isCorrect = rec.yourAnswer && rec.correctAnswer &&
                         rec.yourAnswer.charAt(0) === rec.correctAnswer;

        // 正确答案：只记录题目、选项、正确答案
        if (isCorrect) {
          return `【题目 ${rec.index}】✅ 答案正确
${rec.question}

${rec.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}

正确答案：${rec.correctAnswer}

`;
        }
        // 错误答案或未知：记录完整信息包括解析
        else {
          const statusIcon = rec.yourAnswer === '未获取到' ? '❓' : '❌';
          const statusText = rec.yourAnswer === '未获取到' ? '未答' : '答错';

          return `【题目 ${rec.index}】${statusIcon} ${statusText}
${rec.question}

${rec.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}

你的答案：${rec.yourAnswer}
正确答案：${rec.correctAnswer || '未知'}

📖 解析：
${rec.explanation || '暂无解析'}

${'─'.repeat(60)}

`;
        }
      }).join('');

    // 添加总结
    const summary = `
📋 答题总结：
• 本次共处理 ${totalCount} 道题目
• 其中 ${wasAnsweredCount} 道题目之前已答，直接记录了答案和解析
• 新答了 ${newAnsweredCount} 道题目，使用随机选择策略
• ${onlyRecordedCount} 道题目仅记录了题目内容（可能因为已答无法重新选择）
• ${errorCount} 道题目处理时出现错误

⚠️ 注意事项：
1. 请仔细检查答案的准确性
2. 建议结合解析进行学习
3. 如有疑问请查看原网页确认

🔧 技术信息：
• 使用脚本猫扩展运行
• 自动识别已答/未答状态
• 智能遍历左侧题目列表
• 支持多种页面结构适配

生成时间：${new Date().toLocaleString()}
`;

    const fullContent = header + content + summary;

    const blob = new Blob([fullContent], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    const timestamp = new Date().toISOString().slice(0,16).replace('T', '_').replace(/:/g, '-');
    link.download = `CISP答题记录_${timestamp}_${totalCount}题_${speedMultiplier}x倍速.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();

    updateUI('🎉 导出完成', `${totalCount}/${totalQuestions}`, '记录已保存到下载文件夹');

    // 显示完成提示
    setTimeout(() => {
      const accuracyText = newAnsweredCount > 0 ?
        `\n📈 新答题准确率：${((correctCount / Math.max(newAnsweredCount, 1)) * 100).toFixed(1)}%` : '';

      const message = `🎉 CISP答题助手处理完成！

📊 详细统计：
• 总题数：${totalCount} 道
• 原已答：${wasAnsweredCount} 道 (无需重新答题)
• 新答题：${newAnsweredCount} 道 (脚本自动答题)
• 仅记录：${onlyRecordedCount} 道 (无法答题但已记录)
• 处理错误：${errorCount} 道
• 答对题数：${correctCount} 道${accuracyText}

⚡ 运行参数：
• 倍速设置：${speedMultiplier}x
• 处理时长：约 ${Math.round((Date.now() - (records[0]?.timestamp ? new Date(records[0].timestamp).getTime() : Date.now())) / 60000)} 分钟

📁 文件信息：
• 文件已保存到下载文件夹
• 文件名包含时间戳和题目数量
• 格式规范，内容完整，便于查阅

✅ 建议后续操作：
1. 📖 仔细查看导出的记录文件
2. 🎯 重点学习错题的解析内容
3. 📝 总结知识点，加强记忆
4. 🔄 定期复习，巩固提高`;

      alert(message);
    }, 500);
  }

  // 初始化
  window.addEventListener('load', () => {
    setTimeout(() => {
      createFloatingUI();
      updateNetworkStatus(); // 初始化网络状态
    }, 1000);
  });

  if (document.readyState === 'complete') {
    setTimeout(() => {
      createFloatingUI();
      updateNetworkStatus(); // 初始化网络状态
    }, 1000);
  }

})();
