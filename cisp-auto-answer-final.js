// ==UserScript==
// @name         CISP 自动答题助手 (最终版)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  智能遍历所有题目，识别已答/未答状态，自动答题并导出完整记录
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let currentQuestionIndex = 0;
  let totalQuestions = 0;
  let speedMultiplier = 1; // 倍速控制

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms / speedMultiplier));
  }

  function createFloatingUI() {
    const panel = document.createElement('div');
    panel.id = 'auto-answer-ui';
    panel.style.cssText = `
      position: fixed;
      top: 100px;
      right: 30px;
      background: #fff;
      border: 2px solid #4CAF50;
      border-radius: 10px;
      padding: 15px;
      z-index: 9999;
      box-shadow: 0 0 15px rgba(41, 24, 24, 0.3);
      min-width: 250px;
      font-family: Arial, sans-serif;
    `;
    panel.innerHTML = `
      <div style="font-weight:bold;color:#2E7D32;margin-bottom:10px">🤖 CISP自动答题助手</div>
      <button id="startAutoAnswer" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px">开始自动答题</button>
      <button id="stopAutoAnswer" style="width:100%;padding:8px;background:#f44336;color:white;border:none;border-radius:5px;cursor:pointer;display:none">停止答题</button>
      <div style="margin-bottom:10px">
        <label style="font-size:12px;color:#666">倍速控制：</label>
        <select id="speedControl" style="width:60px;padding:2px;margin-left:5px">
          <option value="1">1x</option>
          <option value="2">2x</option>
          <option value="5">5x</option>
          <option value="10">10x</option>
          <option value="20">20x</option>
        </select>
      </div>
      <button id="debugMode" style="width:100%;padding:6px;background:#2196F3;color:white;border:none;border-radius:5px;cursor:pointer;font-size:12px;margin-bottom:10px">调试模式</button>
      <div id="answer-status" style="color:#555;font-size:14px;margin-bottom:5px">状态：未启动</div>
      <div id="progress-info" style="color:#666;font-size:12px;margin-bottom:5px">进度：0/0</div>
      <div id="current-question" style="color:#333;font-size:11px;background:#f5f5f5;padding:5px;border-radius:3px">当前：未开始</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startAutoAnswer').onclick = startAnswering;
    document.getElementById('stopAutoAnswer').onclick = stopAnswering;
    document.getElementById('debugMode').onclick = toggleDebugMode;
    document.getElementById('speedControl').onchange = function() {
      speedMultiplier = parseInt(this.value);
      console.log(`倍速设置为: ${speedMultiplier}x`);
    };
  }

  function startAnswering() {
    const ok = confirm("🚀 确认开始自动答题？\n\n✅ 系统将自动遍历所有题目\n✅ 识别已答/未答状态\n✅ 随机选择答案（未答题目）\n✅ 记录所有题目和解析\n✅ 完成后自动导出记录\n\n⚠️ 请确保网络稳定，过程中请勿操作页面");
    if (ok) {
      document.getElementById('startAutoAnswer').style.display = 'none';
      document.getElementById('stopAutoAnswer').style.display = 'block';
      updateUI('状态：初始化中...', '0/0', '正在分析页面结构...');
      startAutoAnswering();
    }
  }

  function stopAnswering() {
    answering = false;
    updateUI('状态：已停止', `${records.length}/${totalQuestions}`, '用户手动停止');
    document.getElementById('startAutoAnswer').style.display = 'block';
    document.getElementById('stopAutoAnswer').style.display = 'none';

    // 询问是否导出记录
    if (records.length > 0) {
      const shouldExport = confirm(`已停止答题，共处理了 ${records.length} 道题目。\n\n是否导出答题记录？\n\n记录包含：\n• 题目信息\n• 答案信息\n• 正确答案\n• 解析内容`);
      if (shouldExport) {
        setTimeout(() => exportTxt(), 500);
      }
    } else {
      alert('没有处理任何题目，无记录可导出。');
    }
  }

  let debugMode = false;
  function toggleDebugMode() {
    debugMode = !debugMode;
    const btn = document.getElementById('debugMode');
    btn.textContent = debugMode ? '关闭调试' : '调试模式';
    btn.style.background = debugMode ? '#FF9800' : '#2196F3';
    
    if (debugMode) {
      analyzePageStructure();
    }
  }

  function analyzePageStructure() {
    console.log('=== 页面结构分析 ===');
    
    // 分析题目按钮
    const allButtons = document.querySelectorAll('button');
    const numberButtons = Array.from(allButtons).filter(btn => /^\d+$/.test(btn.textContent.trim()));
    console.log(`找到 ${numberButtons.length} 个数字按钮`);
    
    // 分析题目内容
    const questionElement = document.querySelector('.questionStem');
    console.log('题目元素:', questionElement);
    
    // 分析选项
    const optionElements = document.querySelectorAll('.answerItem');
    console.log(`找到 ${optionElements.length} 个选项`);
    
    updateUI('调试模式', '分析完成', '请查看控制台输出');
  }

  // 智能获取题目按钮列表 - 针对脚本猫和CISP页面优化
  function getQuestionList() {
    let questionButtons = [];

    // 根据截图，左侧题目按钮的多种查找策略
    const strategies = [
      // 策略1: 查找答题卡区域的按钮（根据截图结构）
      () => {
        // 查找答题卡容器
        const answerCard = document.querySelector('.answer-card, .question-card, [class*="答题卡"]');
        if (answerCard) {
          const buttons = answerCard.querySelectorAll('button, div[onclick], span[onclick]');
          return Array.from(buttons).filter(btn => {
            const text = btn.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0;
          });
        }
        return [];
      },

      // 策略2: 直接查找所有数字按钮/元素
      () => {
        const allClickables = document.querySelectorAll('button, div[onclick], span[onclick], [role="button"], .clickable');
        return Array.from(allClickables).filter(el => {
          const text = el.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0 && parseInt(text) <= 2000;
        });
      },

      // 策略3: 查找左侧面板中的数字元素
      () => {
        const leftPanels = document.querySelectorAll('.left-panel, .sidebar, .question-nav, [class*="left"], [class*="side"]');
        for (const panel of leftPanels) {
          const elements = panel.querySelectorAll('*');
          const numElements = Array.from(elements).filter(el => {
            const text = el.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0 && el.offsetWidth > 0 && el.offsetHeight > 0;
          });
          if (numElements.length > 10) return numElements;
        }
        return [];
      },

      // 策略4: 通过父容器特征查找
      () => {
        // 查找包含多个数字的容器
        const containers = document.querySelectorAll('div, section, aside');
        for (const container of containers) {
          const children = container.children;
          const numChildren = Array.from(children).filter(child => {
            const text = child.textContent.trim();
            return /^\d+$/.test(text) && parseInt(text) > 0;
          });
          if (numChildren.length >= 20) { // 假设至少20道题
            return numChildren;
          }
        }
        return [];
      }
    ];

    for (let i = 0; i < strategies.length; i++) {
      questionButtons = strategies[i]();
      if (questionButtons.length > 0) {
        console.log(`策略 ${i + 1} 找到 ${questionButtons.length} 个题目按钮`);
        break;
      }
    }

    // 如果还是没找到，尝试更宽泛的搜索
    if (questionButtons.length === 0) {
      console.log('尝试宽泛搜索...');
      const allElements = document.querySelectorAll('*');
      questionButtons = Array.from(allElements).filter(el => {
        const text = el.textContent.trim();
        const isNumber = /^\d+$/.test(text);
        const isVisible = el.offsetWidth > 0 && el.offsetHeight > 0;
        const isClickable = el.onclick || el.style.cursor === 'pointer' ||
                           el.tagName === 'BUTTON' || el.getAttribute('role') === 'button';
        return isNumber && isVisible && isClickable && parseInt(text) > 0 && parseInt(text) <= 100;
      });
    }

    // 按数字排序
    questionButtons.sort((a, b) => {
      const numA = parseInt(a.textContent.trim());
      const numB = parseInt(b.textContent.trim());
      return numA - numB;
    });

    console.log(`最终找到 ${questionButtons.length} 个题目按钮`);
    return questionButtons;
  }

  // 智能检测题目是否已答 - 根据截图优化
  function isQuestionAnswered(questionButton) {
    const text = questionButton.textContent.trim();
    const classList = questionButton.classList;
    const style = window.getComputedStyle(questionButton);

    // 检查类名 - 根据CISP网站可能的类名
    const answeredClasses = ['answered', 'completed', 'done', 'selected', 'active', 'finished', 'current'];
    const hasAnsweredClass = answeredClasses.some(cls => classList.contains(cls));

    // 检查样式 - 根据截图，已答题目可能有不同的背景色
    const bgColor = style.backgroundColor;
    const color = style.color;
    const borderColor = style.borderColor;

    // 默认未答状态的颜色
    const defaultBgColors = [
      'rgba(0, 0, 0, 0)',
      'transparent',
      'rgb(255, 255, 255)',
      'rgb(248, 249, 250)',
      'rgb(240, 240, 240)'
    ];

    const defaultTextColors = [
      'rgb(0, 0, 0)',
      'rgb(33, 37, 41)',
      'rgba(0, 0, 0, 0.87)',
      'rgb(51, 51, 51)'
    ];

    // 检查是否有特殊样式（通常表示已答）
    const hasSpecialBg = !defaultBgColors.includes(bgColor);
    const hasSpecialColor = !defaultTextColors.includes(color);
    const hasSpecialBorder = borderColor && borderColor !== 'rgb(0, 0, 0)' && borderColor !== 'rgba(0, 0, 0, 0)';

    // 检查是否有红色边框（根据截图，某些题目有红色边框）
    const hasRedBorder = borderColor && (
      borderColor.includes('rgb(255') ||
      borderColor.includes('red') ||
      borderColor.includes('#f') ||
      borderColor.includes('#e')
    );

    // 检查按钮是否被禁用（已答题目可能被禁用）
    const isDisabled = questionButton.disabled || questionButton.hasAttribute('disabled');

    const isAnswered = hasAnsweredClass || hasSpecialBg || hasSpecialColor || hasSpecialBorder || hasRedBorder || isDisabled;

    if (debugMode) {
      console.log(`题目 ${text}: ${isAnswered ? '已答' : '未答'}`);
      console.log(`  类名: ${classList.toString()}`);
      console.log(`  背景: ${bgColor}`);
      console.log(`  文字: ${color}`);
      console.log(`  边框: ${borderColor}`);
      console.log(`  禁用: ${isDisabled}`);
    }

    return isAnswered;
  }

  // 智能获取题目数据
  function getCurrentQuestionData() {
    let qText = '';
    let options = [];
    
    // 获取题目文本
    const questionSelectors = ['.questionStem', '.question-content', '.question-text', '[class*="question"]'];
    for (const selector of questionSelectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.trim().length > 10) {
        qText = element.innerText.trim();
        break;
      }
    }
    
    // 获取选项
    const optionSelectors = ['.answerItem', '.option-item', '.answer-option', '[class*="answer"]'];
    for (const selector of optionSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length >= 2) {
        options = Array.from(elements).map(el => el.innerText.trim()).filter(text => text.length > 0);
        break;
      }
    }
    
    return { qText, options };
  }

  // 智能获取用户答案 - 增强版
  function getUserAnswer() {
    // 多种方式获取用户选择的答案
    const strategies = [
      // 策略1: 查找选中的选项元素
      () => {
        const selectors = [
          '.answerItem.selected',
          '.answerItem.active',
          '.option-item.selected',
          '.answer-option.selected',
          '.choice-item.selected',
          '[class*="answer"][class*="selected"]',
          '[class*="option"][class*="selected"]'
        ];

        for (const sel of selectors) {
          const el = document.querySelector(sel);
          if (el) {
            console.log(`通过选择器 ${sel} 找到答案: ${el.innerText.trim().substring(0, 20)}...`);
            return el.innerText.trim();
          }
        }
        return '';
      },

      // 策略2: 查找选中的radio按钮
      () => {
        const checkedRadio = document.querySelector('input[type="radio"]:checked');
        if (checkedRadio) {
          // 尝试找到对应的标签文本
          const label = document.querySelector(`label[for="${checkedRadio.id}"]`) ||
                       checkedRadio.closest('label') ||
                       checkedRadio.parentElement;
          if (label) {
            console.log(`通过radio按钮找到答案: ${label.innerText.trim().substring(0, 20)}...`);
            return label.innerText.trim();
          }
          return checkedRadio.value || '已选择';
        }
        return '';
      },

      // 策略3: 查找高亮或特殊样式的选项
      () => {
        const allOptions = document.querySelectorAll('.answerItem, .option-item, .answer-option, [class*="answer"]');
        for (const option of allOptions) {
          const style = window.getComputedStyle(option);
          const bgColor = style.backgroundColor;
          const color = style.color;

          // 检查是否有特殊的背景色或文字色（表示选中）
          const hasSpecialBg = bgColor !== 'rgba(0, 0, 0, 0)' &&
                              bgColor !== 'transparent' &&
                              bgColor !== 'rgb(255, 255, 255)';
          const hasSpecialColor = color !== 'rgb(0, 0, 0)';

          if (hasSpecialBg || hasSpecialColor) {
            console.log(`通过样式检测找到答案: ${option.innerText.trim().substring(0, 20)}...`);
            return option.innerText.trim();
          }
        }
        return '';
      }
    ];

    // 依次尝试各种策略
    for (let i = 0; i < strategies.length; i++) {
      const result = strategies[i]();
      if (result) {
        console.log(`策略 ${i + 1} 成功获取到用户答案`);
        return result;
      }
    }

    console.log('未能获取到用户答案');
    return '';
  }

  // 智能获取正确答案和解析
  function getCorrectAnswerAndExplanation() {
    let correctAnswer = '';
    let explanation = '';
    
    // 查找正确答案
    const correctSelectors = ['.isCorrect', '.correct', '[class*="correct"]'];
    for (const selector of correctSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const match = element.innerText.match(/[A-D]/);
        if (match) {
          correctAnswer = match[0];
          break;
        }
      }
    }
    
    // 查找解析
    const explanationSelectors = ['.question-analysis', '.parse-info', '.explanation', '[class*="analysis"]'];
    for (const selector of explanationSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        let text = element.innerText.trim();
        text = text.replace(/^(解析|分析|说明)[:：]?\s*/, '');
        if (text.length > 10) {
          explanation = text;
          break;
        }
      }
    }
    
    return { correctAnswer, explanation };
  }

  // 智能选择选项 - 增强版
  async function selectRandomOption() {
    // 多种选择器尝试找到选项
    const selectors = [
      '.answerItem',
      '.option-item',
      '.answer-option',
      '.choice-item',
      '[class*="answer"]',
      'input[type="radio"]',
      'label[for*="answer"]'
    ];

    let options = [];
    for (const selector of selectors) {
      options = document.querySelectorAll(selector);
      if (options.length >= 2) {
        console.log(`使用选择器 ${selector} 找到 ${options.length} 个选项`);
        break;
      }
    }

    if (options.length === 0) {
      console.log('未找到可选择的选项');
      return false;
    }

    // 检查是否已经选择
    const selectedSelectors = [
      '.answerItem.selected',
      '.option-item.selected',
      '.answer-option.selected',
      '.choice-item.selected',
      'input[type="radio"]:checked',
      '[class*="selected"]'
    ];

    for (const selector of selectedSelectors) {
      const selected = document.querySelector(selector);
      if (selected) {
        console.log('检测到已选择的选项，跳过随机选择');
        return true;
      }
    }

    // 随机选择一个选项
    const index = Math.floor(Math.random() * options.length);
    const selectedOption = options[index];

    console.log(`随机选择第 ${index + 1} 个选项: ${selectedOption.textContent.trim().substring(0, 20)}...`);

    // 尝试多种点击方式
    try {
      // 方式1: 直接点击
      selectedOption.click();
      await delay(300);

      // 方式2: 如果是radio input，确保选中
      const radio = selectedOption.querySelector('input[type="radio"]') ||
                   (selectedOption.tagName === 'INPUT' && selectedOption.type === 'radio' ? selectedOption : null);
      if (radio) {
        radio.checked = true;
        radio.dispatchEvent(new Event('change', { bubbles: true }));
      }

      // 方式3: 触发事件
      selectedOption.dispatchEvent(new Event('click', { bubbles: true }));
      selectedOption.dispatchEvent(new Event('change', { bubbles: true }));

      await delay(500);

      // 验证是否选择成功
      const nowSelected = document.querySelector('.answerItem.selected, .option-item.selected, input[type="radio"]:checked');
      if (nowSelected) {
        console.log('选项选择成功');
        return true;
      } else {
        console.log('选项选择可能失败，但继续处理');
        return true; // 即使检测不到选中状态，也认为尝试过了
      }

    } catch (error) {
      console.error('选择选项时出错:', error);
      return false;
    }
  }

  function updateUI(status, progress, current) {
    document.getElementById('answer-status').innerText = status;
    document.getElementById('progress-info').innerText = progress;
    document.getElementById('current-question').innerText = current;
  }

  // 主要答题流程 - 优化已答题目处理
  async function startAutoAnswering() {
    answering = true;
    records = [];

    try {
      const questionList = getQuestionList();
      totalQuestions = questionList.length;

      if (totalQuestions === 0) {
        updateUI('❌ 错误：未找到题目', '0/0', '请检查页面或使用调试模式');
        return;
      }

      updateUI('🚀 开始答题', `0/${totalQuestions}`, `找到 ${totalQuestions} 道题目`);
      await delay(2000);

      for (let i = 0; i < questionList.length && answering; i++) {
        const questionButton = questionList[i];
        const questionNum = questionButton.textContent.trim();

        updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `正在处理题目 ${questionNum}`);

        try {
          // 点击题目
          questionButton.click();
          await delay(1500);

          // 获取题目数据
          const { qText, options } = getCurrentQuestionData();
          if (!qText || options.length === 0) {
            console.log(`题目 ${questionNum} 数据获取失败，跳过`);
            continue;
          }

          // 检查是否已答（点击后再次检查，因为页面状态可能变化）
          const isAnswered = isQuestionAnswered(questionButton);
          let yourAnswer = '';
          let answerAttempted = false;

          // 先尝试获取已选答案
          yourAnswer = getUserAnswer();

          if (!yourAnswer && !isAnswered) {
            // 如果没有已选答案且题目未答，尝试随机选择
            updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} - 选择答案`);
            answerAttempted = await selectRandomOption();
            if (answerAttempted) {
              await delay(1000);
              yourAnswer = getUserAnswer();
            }
          }

          // 如果题目已答但无法获取答案，尝试从页面其他地方获取
          if (isAnswered && !yourAnswer) {
            // 查找可能显示答案的元素
            const answerElements = document.querySelectorAll('.your-answer, .selected-answer, .user-answer, [class*="answer"]');
            for (const el of answerElements) {
              const text = el.innerText.trim();
              if (text.match(/[A-D]/)) {
                yourAnswer = text;
                break;
              }
            }
          }

          // 等待页面更新，然后获取正确答案和解析
          await delay(800);
          const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();

          // 记录数据
          const record = {
            index: parseInt(questionNum),
            question: qText,
            options,
            yourAnswer: yourAnswer || '未获取到',
            correctAnswer: correctAnswer || '未知',
            explanation: explanation || '暂无解析',
            wasAnswered: isAnswered,
            answerAttempted: answerAttempted,
            timestamp: new Date().toLocaleString()
          };

          records.push(record);

          if (debugMode) {
            console.log(`完成题目 ${questionNum}:`);
            console.log(`  状态: ${isAnswered ? '原已答' : '新答题'}`);
            console.log(`  你的答案: ${yourAnswer}`);
            console.log(`  正确答案: ${correctAnswer}`);
          }

          // 更新状态显示
          const statusText = isAnswered ? '(原已答)' : (answerAttempted ? '(新答题)' : '(仅记录)');
          updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} ${statusText} 完成`);

        } catch (error) {
          console.error(`处理题目 ${questionNum} 时出错:`, error);
          // 即使出错也要记录基本信息
          const { qText, options } = getCurrentQuestionData();
          if (qText) {
            records.push({
              index: parseInt(questionNum),
              question: qText,
              options,
              yourAnswer: '处理出错',
              correctAnswer: '未知',
              explanation: `处理出错: ${error.message}`,
              wasAnswered: false,
              answerAttempted: false,
              timestamp: new Date().toLocaleString()
            });
          }
        }

        await delay(500);
      }

      updateUI('✅ 完成答题', `${records.length}/${totalQuestions}`, '正在导出记录...');
      await delay(1000);
      exportTxt();

    } catch (error) {
      console.error('答题流程出错:', error);
      updateUI('❌ 出现错误', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startAutoAnswer').style.display = 'block';
      document.getElementById('stopAutoAnswer').style.display = 'none';
    }
  }

  function exportTxt() {
    if (records.length === 0) {
      alert('没有记录可导出');
      return;
    }

    // 统计信息
    const totalCount = records.length;
    const wasAnsweredCount = records.filter(r => r.wasAnswered).length;
    const newAnsweredCount = records.filter(r => r.answerAttempted).length;
    const onlyRecordedCount = records.filter(r => !r.wasAnswered && !r.answerAttempted).length;
    const errorCount = records.filter(r => r.yourAnswer === '处理出错').length;
    const correctCount = records.filter(r => r.yourAnswer && r.correctAnswer &&
      r.yourAnswer.charAt(0) === r.correctAnswer).length;

    const header = `CISP自动答题记录 - 最终版本
${'='.repeat(80)}
📋 基本信息
生成时间：${new Date().toLocaleString()}
脚本版本：3.0 (最终优化版)
倍速设置：${speedMultiplier}x
网站地址：${window.location.href}

📊 统计信息
• 总题数：${totalCount} 道
• 原已答：${wasAnsweredCount} 道 (无需重新答题)
• 新答题：${newAnsweredCount} 道 (脚本自动答题)
• 仅记录：${onlyRecordedCount} 道 (无法答题但已记录)
• 处理错误：${errorCount} 道
• 答对题数：${correctCount} 道 (基于已知正确答案)

📈 准确率分析
${newAnsweredCount > 0 ? `新答题准确率：${((correctCount / Math.max(newAnsweredCount, 1)) * 100).toFixed(1)}%` : '无新答题'}
${totalCount > 0 ? `总体准确率：${((correctCount / totalCount) * 100).toFixed(1)}%` : ''}

${'='.repeat(80)}

`;

    const content = records.map((rec) => {
      let statusIcon = '';
      let statusText = '';
      let isCorrect = false;

      if (rec.yourAnswer === '处理出错') {
        statusIcon = '❌';
        statusText = '处理出错';
      } else if (rec.wasAnswered) {
        statusIcon = '🔄';
        statusText = '原已答';
      } else if (rec.answerAttempted) {
        statusIcon = '✨';
        statusText = '新答题';
      } else {
        statusIcon = '📝';
        statusText = '仅记录';
      }

      // 检查答案是否正确
      if (rec.yourAnswer && rec.correctAnswer) {
        isCorrect = rec.yourAnswer.charAt(0) === rec.correctAnswer;
      }

      const correctIcon = isCorrect ? '✅' : (rec.correctAnswer ? '❌' : '❓');

      return `
┌─ 题目 ${rec.index} ─────────────────────────────────────────────────────
│ 状态：${statusIcon} ${statusText} ${correctIcon} ${isCorrect ? '答案正确' : (rec.correctAnswer ? '答案错误' : '未知正确性')}
│ 时间：${rec.timestamp}
└─────────────────────────────────────────────────────────────────────

📝 题目内容：
${rec.question}

📋 选项列表：
${rec.options.map((opt, i) => `   ${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}

💡 答案信息：
   你的答案：${rec.yourAnswer || '❓ 未获取到'}
   正确答案：${rec.correctAnswer || '❓ 未知'}
   ${isCorrect ? '🎉 恭喜答对了！' : (rec.correctAnswer && rec.yourAnswer ? '💪 继续加油！' : '')}

📖 详细解析：
${rec.explanation || '暂无解析内容'}

${'═'.repeat(80)}
`;
    }).join('');

    // 添加总结
    const summary = `
📋 答题总结：
• 本次共处理 ${totalCount} 道题目
• 其中 ${wasAnsweredCount} 道题目之前已答，直接记录了答案和解析
• 新答了 ${newAnsweredCount} 道题目，使用随机选择策略
• ${onlyRecordedCount} 道题目仅记录了题目内容（可能因为已答无法重新选择）
• ${errorCount} 道题目处理时出现错误

⚠️ 注意事项：
1. 请仔细检查答案的准确性
2. 建议结合解析进行学习
3. 如有疑问请查看原网页确认

🔧 技术信息：
• 使用脚本猫扩展运行
• 自动识别已答/未答状态
• 智能遍历左侧题目列表
• 支持多种页面结构适配

生成时间：${new Date().toLocaleString()}
`;

    const fullContent = header + content + summary;

    const blob = new Blob([fullContent], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    const timestamp = new Date().toISOString().slice(0,16).replace('T', '_').replace(/:/g, '-');
    link.download = `CISP答题记录_${timestamp}_${totalCount}题_${speedMultiplier}x倍速.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();

    updateUI('🎉 导出完成', `${totalCount}/${totalQuestions}`, '记录已保存到下载文件夹');

    // 显示完成提示
    setTimeout(() => {
      const accuracyText = newAnsweredCount > 0 ?
        `\n📈 新答题准确率：${((correctCount / Math.max(newAnsweredCount, 1)) * 100).toFixed(1)}%` : '';

      const message = `🎉 CISP答题助手处理完成！

📊 详细统计：
• 总题数：${totalCount} 道
• 原已答：${wasAnsweredCount} 道 (无需重新答题)
• 新答题：${newAnsweredCount} 道 (脚本自动答题)
• 仅记录：${onlyRecordedCount} 道 (无法答题但已记录)
• 处理错误：${errorCount} 道
• 答对题数：${correctCount} 道${accuracyText}

⚡ 运行参数：
• 倍速设置：${speedMultiplier}x
• 处理时长：约 ${Math.round((Date.now() - (records[0]?.timestamp ? new Date(records[0].timestamp).getTime() : Date.now())) / 60000)} 分钟

📁 文件信息：
• 文件已保存到下载文件夹
• 文件名包含时间戳和题目数量
• 格式规范，内容完整，便于查阅

✅ 建议后续操作：
1. 📖 仔细查看导出的记录文件
2. 🎯 重点学习错题的解析内容
3. 📝 总结知识点，加强记忆
4. 🔄 定期复习，巩固提高`;

      alert(message);
    }, 500);
  }

  // 初始化
  window.addEventListener('load', () => setTimeout(createFloatingUI, 1000));
  if (document.readyState === 'complete') setTimeout(createFloatingUI, 1000);

})();
