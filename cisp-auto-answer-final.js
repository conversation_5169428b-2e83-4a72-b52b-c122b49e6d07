// ==UserScript==
// @name         CISP 自动答题助手 (最终版)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  智能遍历所有题目，识别已答/未答状态，自动答题并导出完整记录
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let currentQuestionIndex = 0;
  let totalQuestions = 0;

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  function createFloatingUI() {
    const panel = document.createElement('div');
    panel.id = 'auto-answer-ui';
    panel.style.cssText = `
      position: fixed;
      top: 100px;
      right: 30px;
      background: #fff;
      border: 2px solid #4CAF50;
      border-radius: 10px;
      padding: 15px;
      z-index: 9999;
      box-shadow: 0 0 15px rgba(0,0,0,0.3);
      min-width: 250px;
      font-family: Arial, sans-serif;
    `;
    panel.innerHTML = `
      <div style="font-weight:bold;color:#2E7D32;margin-bottom:10px">🤖 CISP自动答题助手</div>
      <button id="startAutoAnswer" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px">开始自动答题</button>
      <button id="stopAutoAnswer" style="width:100%;padding:8px;background:#f44336;color:white;border:none;border-radius:5px;cursor:pointer;display:none">停止答题</button>
      <button id="debugMode" style="width:100%;padding:6px;background:#2196F3;color:white;border:none;border-radius:5px;cursor:pointer;font-size:12px;margin-bottom:10px">调试模式</button>
      <div id="answer-status" style="color:#555;font-size:14px;margin-bottom:5px">状态：未启动</div>
      <div id="progress-info" style="color:#666;font-size:12px;margin-bottom:5px">进度：0/0</div>
      <div id="current-question" style="color:#333;font-size:11px;background:#f5f5f5;padding:5px;border-radius:3px">当前：未开始</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startAutoAnswer').onclick = startAnswering;
    document.getElementById('stopAutoAnswer').onclick = stopAnswering;
    document.getElementById('debugMode').onclick = toggleDebugMode;
  }

  function startAnswering() {
    const ok = confirm("🚀 确认开始自动答题？\n\n✅ 系统将自动遍历所有题目\n✅ 识别已答/未答状态\n✅ 随机选择答案（未答题目）\n✅ 记录所有题目和解析\n✅ 完成后自动导出记录\n\n⚠️ 请确保网络稳定，过程中请勿操作页面");
    if (ok) {
      document.getElementById('startAutoAnswer').style.display = 'none';
      document.getElementById('stopAutoAnswer').style.display = 'block';
      updateUI('状态：初始化中...', '0/0', '正在分析页面结构...');
      startAutoAnswering();
    }
  }

  function stopAnswering() {
    answering = false;
    updateUI('状态：已停止', `${records.length}/${totalQuestions}`, '用户手动停止');
    document.getElementById('startAutoAnswer').style.display = 'block';
    document.getElementById('stopAutoAnswer').style.display = 'none';
    if (records.length > 0) {
      setTimeout(() => exportTxt(), 1000);
    }
  }

  let debugMode = false;
  function toggleDebugMode() {
    debugMode = !debugMode;
    const btn = document.getElementById('debugMode');
    btn.textContent = debugMode ? '关闭调试' : '调试模式';
    btn.style.background = debugMode ? '#FF9800' : '#2196F3';
    
    if (debugMode) {
      analyzePageStructure();
    }
  }

  function analyzePageStructure() {
    console.log('=== 页面结构分析 ===');
    
    // 分析题目按钮
    const allButtons = document.querySelectorAll('button');
    const numberButtons = Array.from(allButtons).filter(btn => /^\d+$/.test(btn.textContent.trim()));
    console.log(`找到 ${numberButtons.length} 个数字按钮`);
    
    // 分析题目内容
    const questionElement = document.querySelector('.questionStem');
    console.log('题目元素:', questionElement);
    
    // 分析选项
    const optionElements = document.querySelectorAll('.answerItem');
    console.log(`找到 ${optionElements.length} 个选项`);
    
    updateUI('调试模式', '分析完成', '请查看控制台输出');
  }

  // 智能获取题目按钮列表
  function getQuestionList() {
    let questionButtons = [];
    
    // 多种策略查找题目按钮
    const strategies = [
      // 策略1: 直接查找数字按钮
      () => {
        const allButtons = document.querySelectorAll('button');
        return Array.from(allButtons).filter(btn => {
          const text = btn.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0 && parseInt(text) <= 2000;
        });
      },
      
      // 策略2: 在特定容器中查找
      () => {
        const containers = document.querySelectorAll('.sidebar, .left-panel, .question-nav, [class*="nav"]');
        for (const container of containers) {
          const buttons = container.querySelectorAll('button');
          const numButtons = Array.from(buttons).filter(btn => /^\d+$/.test(btn.textContent.trim()));
          if (numButtons.length > 10) return numButtons; // 假设至少有10道题
        }
        return [];
      },
      
      // 策略3: 查找包含数字的可点击元素
      () => {
        const clickables = document.querySelectorAll('div[onclick], span[onclick], a, [role="button"]');
        return Array.from(clickables).filter(el => /^\d+$/.test(el.textContent.trim()));
      }
    ];
    
    for (const strategy of strategies) {
      questionButtons = strategy();
      if (questionButtons.length > 0) {
        console.log(`使用策略找到 ${questionButtons.length} 个题目按钮`);
        break;
      }
    }
    
    // 按数字排序
    questionButtons.sort((a, b) => {
      const numA = parseInt(a.textContent.trim());
      const numB = parseInt(b.textContent.trim());
      return numA - numB;
    });
    
    return questionButtons;
  }

  // 智能检测题目是否已答
  function isQuestionAnswered(questionButton) {
    const text = questionButton.textContent.trim();
    const classList = questionButton.classList;
    const style = window.getComputedStyle(questionButton);
    
    // 检查类名
    const answeredClasses = ['answered', 'completed', 'done', 'selected', 'active', 'finished'];
    const hasAnsweredClass = answeredClasses.some(cls => classList.contains(cls));
    
    // 检查样式
    const bgColor = style.backgroundColor;
    const color = style.color;
    const defaultColors = ['rgba(0, 0, 0, 0)', 'transparent', 'rgb(255, 255, 255)', 'rgb(248, 249, 250)'];
    const hasSpecialStyle = !defaultColors.includes(bgColor) || color !== 'rgb(0, 0, 0)';
    
    const isAnswered = hasAnsweredClass || hasSpecialStyle;
    
    if (debugMode) {
      console.log(`题目 ${text}: ${isAnswered ? '已答' : '未答'} (类名: ${classList.toString()}, 背景: ${bgColor})`);
    }
    
    return isAnswered;
  }

  // 智能获取题目数据
  function getCurrentQuestionData() {
    let qText = '';
    let options = [];
    
    // 获取题目文本
    const questionSelectors = ['.questionStem', '.question-content', '.question-text', '[class*="question"]'];
    for (const selector of questionSelectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.trim().length > 10) {
        qText = element.innerText.trim();
        break;
      }
    }
    
    // 获取选项
    const optionSelectors = ['.answerItem', '.option-item', '.answer-option', '[class*="answer"]'];
    for (const selector of optionSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length >= 2) {
        options = Array.from(elements).map(el => el.innerText.trim()).filter(text => text.length > 0);
        break;
      }
    }
    
    return { qText, options };
  }

  // 智能获取用户答案
  function getUserAnswer() {
    const selectedSelectors = [
      '.answerItem.selected', '.answerItem.active',
      '.option-item.selected', '.answer-option.selected',
      '[class*="answer"][class*="selected"]'
    ];
    
    for (const selector of selectedSelectors) {
      const selected = document.querySelector(selector);
      if (selected) return selected.innerText.trim();
    }
    return '';
  }

  // 智能获取正确答案和解析
  function getCorrectAnswerAndExplanation() {
    let correctAnswer = '';
    let explanation = '';
    
    // 查找正确答案
    const correctSelectors = ['.isCorrect', '.correct', '[class*="correct"]'];
    for (const selector of correctSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const match = element.innerText.match(/[A-D]/);
        if (match) {
          correctAnswer = match[0];
          break;
        }
      }
    }
    
    // 查找解析
    const explanationSelectors = ['.question-analysis', '.parse-info', '.explanation', '[class*="analysis"]'];
    for (const selector of explanationSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        let text = element.innerText.trim();
        text = text.replace(/^(解析|分析|说明)[:：]?\s*/, '');
        if (text.length > 10) {
          explanation = text;
          break;
        }
      }
    }
    
    return { correctAnswer, explanation };
  }

  // 智能选择选项
  async function selectRandomOption() {
    const options = document.querySelectorAll('.answerItem');
    if (options.length === 0) return false;
    
    // 检查是否已选择
    const alreadySelected = document.querySelector('.answerItem.selected');
    if (alreadySelected) return true;
    
    // 随机选择
    const index = Math.floor(Math.random() * options.length);
    options[index].click();
    await delay(800);
    return true;
  }

  function updateUI(status, progress, current) {
    document.getElementById('answer-status').innerText = status;
    document.getElementById('progress-info').innerText = progress;
    document.getElementById('current-question').innerText = current;
  }

  // 主要答题流程
  async function startAutoAnswering() {
    answering = true;
    records = [];
    
    try {
      const questionList = getQuestionList();
      totalQuestions = questionList.length;
      
      if (totalQuestions === 0) {
        updateUI('❌ 错误：未找到题目', '0/0', '请检查页面或使用调试模式');
        return;
      }
      
      updateUI('🚀 开始答题', `0/${totalQuestions}`, `找到 ${totalQuestions} 道题目`);
      await delay(2000);
      
      for (let i = 0; i < questionList.length && answering; i++) {
        const questionButton = questionList[i];
        const questionNum = questionButton.textContent.trim();
        
        updateUI('📝 答题中', `${i + 1}/${totalQuestions}`, `正在处理题目 ${questionNum}`);
        
        // 点击题目
        questionButton.click();
        await delay(1500);
        
        // 获取题目数据
        const { qText, options } = getCurrentQuestionData();
        if (!qText || options.length === 0) {
          console.log(`题目 ${questionNum} 数据获取失败`);
          continue;
        }
        
        // 检查是否已答
        const isAnswered = isQuestionAnswered(questionButton);
        let yourAnswer = '';
        
        if (!isAnswered) {
          // 未答题目，随机选择答案
          await selectRandomOption();
          await delay(1000);
        }
        
        yourAnswer = getUserAnswer();
        
        // 获取正确答案和解析
        const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();
        
        // 记录数据
        records.push({
          index: parseInt(questionNum),
          question: qText,
          options,
          yourAnswer,
          correctAnswer,
          explanation,
          wasAnswered: isAnswered,
          timestamp: new Date().toLocaleString()
        });
        
        if (debugMode) {
          console.log(`完成题目 ${questionNum}: ${isAnswered ? '原已答' : '新答题'}`);
        }
        
        await delay(500);
      }
      
      updateUI('✅ 完成答题', `${records.length}/${totalQuestions}`, '正在导出记录...');
      await delay(1000);
      exportTxt();
      
    } catch (error) {
      console.error('答题出错:', error);
      updateUI('❌ 出现错误', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startAutoAnswer').style.display = 'block';
      document.getElementById('stopAutoAnswer').style.display = 'none';
    }
  }

  function exportTxt() {
    if (records.length === 0) {
      alert('没有记录可导出');
      return;
    }
    
    const header = `CISP自动答题记录\n生成时间：${new Date().toLocaleString()}\n总题数：${records.length}\n原已答：${records.filter(r => r.wasAnswered).length}\n新答题：${records.filter(r => !r.wasAnswered).length}\n${'='.repeat(60)}\n\n`;
    
    const content = records.map(rec => 
      `【题目 ${rec.index}】${rec.wasAnswered ? ' 🔄 原已答' : ' ✨ 新答题'}\n` +
      `${rec.question}\n\n` +
      `${rec.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}\n\n` +
      `你的答案：${rec.yourAnswer || '未选择'}\n` +
      `正确答案：${rec.correctAnswer || '未知'}\n` +
      `解析：${rec.explanation || '暂无解析'}\n` +
      `处理时间：${rec.timestamp}\n` +
      `${'='.repeat(60)}\n\n`
    ).join('');

    const blob = new Blob([header + content], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    link.download = `CISP答题记录_${new Date().toISOString().slice(0,10)}_${records.length}题.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();
    
    updateUI('🎉 导出完成', `${records.length}/${totalQuestions}`, '记录已保存到下载文件夹');
    
    // 显示完成提示
    setTimeout(() => {
      alert(`🎉 答题完成！\n\n📊 统计信息：\n• 总题数：${records.length}\n• 原已答：${records.filter(r => r.wasAnswered).length}\n• 新答题：${records.filter(r => !r.wasAnswered).length}\n\n📁 记录已导出到下载文件夹`);
    }, 500);
  }

  // 初始化
  window.addEventListener('load', () => setTimeout(createFloatingUI, 1000));
  if (document.readyState === 'complete') setTimeout(createFloatingUI, 1000);

})();
