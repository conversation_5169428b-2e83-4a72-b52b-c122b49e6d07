// ==UserScript==
// @name         CISP 页面元素调试助手
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  帮助识别CISP网站的页面元素结构，用于优化自动答题脚本
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  function createDebugUI() {
    const panel = document.createElement('div');
    panel.id = 'debug-ui';
    panel.style.cssText = `
      position: fixed;
      top: 50px;
      left: 30px;
      background: #fff;
      border: 2px solid #2196F3;
      border-radius: 10px;
      padding: 15px;
      z-index: 9999;
      box-shadow: 0 0 10px rgba(0,0,0,0.3);
      max-width: 400px;
      max-height: 500px;
      overflow-y: auto;
    `;
    panel.innerHTML = `
      <div><strong>CISP 页面元素调试</strong></div>
      <button id="analyzeQuestions" style="margin:5px;padding:5px 10px">分析题目按钮</button>
      <button id="analyzeContent" style="margin:5px;padding:5px 10px">分析题目内容</button>
      <button id="analyzeOptions" style="margin:5px;padding:5px 10px">分析选项</button>
      <button id="analyzeAnswers" style="margin:5px;padding:5px 10px">分析答案状态</button>
      <div id="debug-output" style="margin-top:10px;font-size:12px;background:#f5f5f5;padding:10px;border-radius:5px;white-space:pre-wrap;max-height:300px;overflow-y:auto"></div>
    `;
    document.body.appendChild(panel);

    document.getElementById('analyzeQuestions').onclick = analyzeQuestionButtons;
    document.getElementById('analyzeContent').onclick = analyzeQuestionContent;
    document.getElementById('analyzeOptions').onclick = analyzeOptions;
    document.getElementById('analyzeAnswers').onclick = analyzeAnswerStatus;
  }

  function log(message) {
    const output = document.getElementById('debug-output');
    output.textContent += message + '\n';
    output.scrollTop = output.scrollHeight;
    console.log(message);
  }

  function clearLog() {
    document.getElementById('debug-output').textContent = '';
  }

  function analyzeQuestionButtons() {
    clearLog();
    log('=== 分析题目按钮 ===');
    
    // 查找所有可能的按钮
    const allButtons = document.querySelectorAll('button');
    log(`总共找到 ${allButtons.length} 个按钮`);
    
    // 过滤数字按钮
    const numberButtons = Array.from(allButtons).filter(btn => {
      const text = btn.textContent.trim();
      return /^\d+$/.test(text);
    });
    
    log(`找到 ${numberButtons.length} 个数字按钮:`);
    numberButtons.forEach((btn, index) => {
      const text = btn.textContent.trim();
      const classes = btn.className;
      const style = window.getComputedStyle(btn);
      const bgColor = style.backgroundColor;
      const color = style.color;
      
      log(`${index + 1}. 按钮 "${text}"`);
      log(`   类名: ${classes}`);
      log(`   背景色: ${bgColor}`);
      log(`   文字色: ${color}`);
      log(`   父元素: ${btn.parentElement?.tagName}.${btn.parentElement?.className}`);
      log('');
    });
    
    // 分析容器
    log('=== 可能的容器 ===');
    const containers = document.querySelectorAll('.sidebar, .left-panel, .question-container, [class*="nav"], [class*="question"]');
    containers.forEach((container, index) => {
      log(`${index + 1}. 容器: ${container.tagName}.${container.className}`);
      const buttons = container.querySelectorAll('button');
      log(`   包含 ${buttons.length} 个按钮`);
    });
  }

  function analyzeQuestionContent() {
    clearLog();
    log('=== 分析题目内容 ===');
    
    // 查找题目文本
    const possibleSelectors = [
      '.questionStem',
      '.question-content',
      '.question-text',
      '.question-stem',
      '[class*="question"][class*="stem"]',
      '.problem-content',
      '.question',
      '[class*="question"]'
    ];
    
    possibleSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`选择器 "${selector}" 找到 ${elements.length} 个元素:`);
        elements.forEach((el, index) => {
          const text = el.innerText.trim();
          log(`  ${index + 1}. ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
        });
      }
    });
    
    // 查找所有包含文本的元素
    log('\n=== 包含题目关键词的元素 ===');
    const allElements = document.querySelectorAll('*');
    const questionElements = Array.from(allElements).filter(el => {
      const text = el.innerText;
      return text && (text.includes('TCP') || text.includes('POP3') || text.includes('端口') || text.includes('在TCP'));
    });
    
    questionElements.forEach((el, index) => {
      if (index < 5) { // 只显示前5个
        log(`${index + 1}. ${el.tagName}.${el.className}`);
        log(`   文本: ${el.innerText.substring(0, 100)}...`);
      }
    });
  }

  function analyzeOptions() {
    clearLog();
    log('=== 分析选项 ===');
    
    const possibleSelectors = [
      '.answerItem',
      '.option-item',
      '.answer-option',
      '.choice-item',
      '[class*="answer"][class*="item"]',
      '[class*="option"]',
      '[class*="choice"]'
    ];
    
    possibleSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`选择器 "${selector}" 找到 ${elements.length} 个选项:`);
        elements.forEach((el, index) => {
          const text = el.innerText.trim();
          const classes = el.className;
          const isSelected = classes.includes('selected') || classes.includes('active');
          log(`  ${index + 1}. ${text} ${isSelected ? '[已选]' : ''}`);
          log(`     类名: ${classes}`);
        });
        log('');
      }
    });
  }

  function analyzeAnswerStatus() {
    clearLog();
    log('=== 分析答案状态 ===');
    
    // 查找正确答案
    const correctSelectors = [
      '.isCorrect',
      '.correct',
      '.right-answer',
      '[class*="correct"]',
      '.answer-result',
      '[class*="result"]'
    ];
    
    correctSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`正确答案选择器 "${selector}" 找到 ${elements.length} 个元素:`);
        elements.forEach((el, index) => {
          const text = el.innerText.trim();
          log(`  ${index + 1}. ${text}`);
        });
        log('');
      }
    });
    
    // 查找解析
    const explanationSelectors = [
      '.question-analysis',
      '.parse-info',
      '.explanation',
      '.analysis',
      '.answer-explanation',
      '[class*="analysis"]',
      '[class*="explanation"]',
      '[class*="parse"]'
    ];
    
    explanationSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`解析选择器 "${selector}" 找到 ${elements.length} 个元素:`);
        elements.forEach((el, index) => {
          const text = el.innerText.trim();
          log(`  ${index + 1}. ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
        });
        log('');
      }
    });
    
    // 查找包含"解析"的元素
    log('=== 包含"解析"关键词的元素 ===');
    const allElements = document.querySelectorAll('*');
    const analysisElements = Array.from(allElements).filter(el => {
      const text = el.innerText;
      return text && text.includes('解析') && text.length < 1000;
    });
    
    analysisElements.forEach((el, index) => {
      if (index < 3) {
        log(`${index + 1}. ${el.tagName}.${el.className}`);
        log(`   文本: ${el.innerText.substring(0, 200)}...`);
        log('');
      }
    });
  }

  // 页面加载完成后初始化
  window.addEventListener('load', () => {
    setTimeout(createDebugUI, 1000);
  });

  if (document.readyState === 'complete') {
    setTimeout(createDebugUI, 1000);
  }

})();
