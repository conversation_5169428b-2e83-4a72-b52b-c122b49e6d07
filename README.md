# CISP网络安全知识竞赛题目爬虫 - 完整版

这个工具用于爬取CISP网络安全知识竞赛练习题的内容。

## 🎯 爬取结果摘要

**目标网址**: https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN

**爬取成果**:
- ✅ 成功提取 **1个完整题目**
- 📊 题目编号: 81
- 🔐 题目类型: 公钥密码相关选择题
- 📝 包含完整的题目、选项、答案和解析

## 📋 提取到的题目示例

**题目81**: 下列不属于公钥密码的特点的是（）。

**选项**:
- A. 公钥私钥成对出现
- B. 加密密钥和解密密钥相同
- C. 公钥加密私钥解密-机密性
- D. 私钥加密公钥解密-数字签名

**正确答案**: B
**解析**: 公钥密码的加密密钥和解密密钥不同。

## 🚀 快速开始

### 方法1：一键运行（推荐）
```bash
# 双击运行批处理文件
run.bat
```

### 方法2：手动执行
```bash
# 1. 安装依赖
npm install

# 2. 安装浏览器
npx playwright install chromium

# 3. 运行爬虫（选择一个）
node simple_scraper.js      # 基础版本
node advanced_scraper.js    # 高级版本
node enhanced_scraper.js    # 增强版本

# 4. 解析和整理数据
node question_parser.js     # 解析题目
node final_summary.js       # 生成最终报告
```

## 📁 输出文件说明

### 核心结果文件
- `final_report.txt` - **最终爬取报告**（推荐查看）
- `final_questions.csv` - **Excel兼容的题目数据**
- `parsed_questions.txt` - **格式化的题目内容**

### 原始数据文件
- `questions_detailed.json` - 原始提取的JSON数据
- `page_screenshot.png` - 页面截图
- `page_source.html` - 页面HTML源码

### 调试文件
- `api_response_*.json` - 拦截到的API响应
- `debug_*.html/png` - 调试信息

## 🔧 技术实现

### 使用的技术栈
- **Playwright** - 无头浏览器自动化
- **Node.js** - JavaScript运行环境
- **多策略提取** - CSS选择器 + 文本搜索 + API拦截

### 提取策略详解

#### 1. CSS选择器匹配
```javascript
const selectors = [
    '.question-item', '.practice-question',
    '.exam-question', '.question-container',
    '[class*="question"]', '.answer-header'
];
```

#### 2. 文本内容搜索
```javascript
const keywords = ['题', '问', '选择', '判断', 'A.', 'B.', '正确', '错误'];
```

#### 3. API请求拦截
监听网络请求，自动保存API响应数据用于分析。

## ⚠️ 遇到的挑战

1. **登录限制** - 网站需要登录才能访问完整内容
2. **动态加载** - 页面使用JavaScript动态生成内容
3. **API认证** - 大部分数据接口需要身份验证
4. **反爬机制** - 网站可能有反爬虫保护

## 💡 改进建议

### 获取更多题目的方法：

1. **手动登录**
   - 运行爬虫后在浏览器中手动登录
   - 登录成功后按回车继续爬取

2. **使用浏览器扩展**
   - 开发Chrome/Firefox扩展
   - 在已登录状态下提取页面数据

3. **API逆向分析**
   - 分析网站的API接口
   - 直接调用数据接口获取题目

4. **增加等待时间**
   - 修改脚本中的等待时间
   - 确保页面完全加载

## 📊 数据格式

### CSV格式（Excel兼容）
```csv
题目编号,题目内容,选项A,选项B,选项C,选项D,正确答案,解析
81,"下列不属于公钥密码的特点的是（）。","公钥私钥成对出现","加密密钥和解密密钥相同",...
```

### JSON格式
```json
{
  "number": "81",
  "content": "下列不属于公钥密码的特点的是（）。",
  "options": [
    {"letter": "A", "text": "公钥私钥成对出现"},
    {"letter": "B", "text": "加密密钥和解密密钥相同"}
  ],
  "correctAnswer": "B",
  "explanation": "公钥密码的加密密钥和解密密钥不同。"
}
```

## 🔍 故障排除

### 没有提取到题目？
1. 检查网络连接
2. 确认是否需要登录
3. 查看 `page_screenshot.png` 了解页面状态
4. 检查控制台错误信息

### 提取内容不完整？
1. 增加页面等待时间
2. 手动登录后重新运行
3. 检查API响应文件
4. 调整CSS选择器

## 📞 技术支持

如果遇到问题，请检查以下文件：
- `final_report.txt` - 查看详细的爬取报告
- `README.md` - 本使用说明
- 控制台输出 - 查看实时错误信息

## ⚖️ 使用声明

- 请遵守目标网站的使用条款
- 仅用于学习和研究目的
- 避免对服务器造成过大压力
- 尊重网站的反爬虫机制

## 📄 许可证

MIT License - 详见 LICENSE 文件
