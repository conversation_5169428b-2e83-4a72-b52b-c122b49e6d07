const { chromium } = require('playwright');
const fs = require('fs');

class EnhancedCISPScraper {
    constructor() {
        this.browser = null;
        this.page = null;
        this.questions = [];
    }

    async init() {
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        
        this.page = await this.browser.newPage();
        
        // 设置更长的超时时间
        this.page.setDefaultTimeout(60000);
        
        // 监听网络请求
        this.page.on('response', async (response) => {
            const url = response.url();
            if (url.includes('api') || url.includes('question') || url.includes('practice')) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        console.log('发现API响应:', url);
                        
                        // 保存API响应
                        const timestamp = Date.now();
                        fs.writeFileSync(`api_response_${timestamp}.json`, JSON.stringify(data, null, 2));
                        
                        // 尝试从API数据中提取题目
                        this.extractFromAPIData(data, url);
                    }
                } catch (e) {
                    console.log('解析API响应失败:', e.message);
                }
            }
        });
    }

    async navigateAndWait() {
        const url = 'https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN?product_id=course_2wz9KwPb5MUq4W63laK2HNYGSDU&task_id=pt_68215ca6e4cfe_kd4hq1pC';
        
        console.log('正在访问目标网页...');
        await this.page.goto(url, {
            waitUntil: 'networkidle',
            timeout: 60000
        });
        
        // 等待页面完全加载
        await this.page.waitForTimeout(5000);
        
        // 检查当前页面状态
        const currentUrl = this.page.url();
        const title = await this.page.title();
        
        console.log('当前URL:', currentUrl);
        console.log('页面标题:', title);
        
        // 如果重定向到了登录页面，提示用户
        if (currentUrl.includes('login') || title.includes('登录')) {
            console.log('页面重定向到登录页面，需要手动登录');
            console.log('请在浏览器中完成登录，然后按回车继续...');
            await this.waitForUserInput();
            
            // 登录后重新导航到目标页面
            await this.page.goto(url, {
                waitUntil: 'networkidle',
                timeout: 60000
            });
            await this.page.waitForTimeout(5000);
        }
    }

    async waitForUserInput() {
        return new Promise((resolve) => {
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            rl.question('按回车键继续...', () => {
                rl.close();
                resolve();
            });
        });
    }

    async extractQuestions() {
        console.log('开始提取题目...');
        
        // 等待内容加载
        await this.page.waitForTimeout(3000);
        
        // 尝试多种提取方法
        await this.extractBySelectors();
        await this.extractByScrolling();
        await this.extractByInteraction();
        
        return this.questions;
    }

    async extractBySelectors() {
        console.log('方法1: 使用CSS选择器提取...');
        
        const selectors = [
            '.question-item',
            '.practice-question',
            '.exam-question',
            '.question-container',
            '.question-box',
            '.question',
            '.item',
            '[data-question]',
            '[class*="question"]',
            '[class*="题"]',
            '.list-item',
            '.content-item',
            '.answer-header',
            '.question-header',
            '.question-content'
        ];

        for (const selector of selectors) {
            try {
                const elements = await this.page.$$(selector);
                if (elements.length > 0) {
                    console.log(`选择器 ${selector} 找到 ${elements.length} 个元素`);
                    
                    for (let i = 0; i < elements.length; i++) {
                        const element = elements[i];
                        const text = await element.textContent();
                        const html = await element.innerHTML();
                        
                        if (text && text.trim().length > 30) {
                            // 检查是否包含题目特征
                            if (this.isQuestionText(text)) {
                                this.questions.push({
                                    method: 'selector',
                                    selector: selector,
                                    index: i + 1,
                                    text: text.trim(),
                                    html: html,
                                    extractedAt: new Date().toISOString()
                                });
                            }
                        }
                    }
                }
            } catch (e) {
                console.log(`选择器 ${selector} 失败:`, e.message);
            }
        }
    }

    async extractByScrolling() {
        console.log('方法2: 通过滚动加载更多内容...');
        
        // 滚动到页面底部
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        
        await this.page.waitForTimeout(3000);
        
        // 尝试点击"加载更多"或"下一页"按钮
        const loadMoreSelectors = [
            'text=加载更多',
            'text=下一页',
            'text=更多',
            '.load-more',
            '.next-page',
            '[class*="load"]',
            '[class*="more"]',
            '[class*="next"]'
        ];
        
        for (const selector of loadMoreSelectors) {
            try {
                const button = await this.page.$(selector);
                if (button) {
                    console.log(`找到加载更多按钮: ${selector}`);
                    await button.click();
                    await this.page.waitForTimeout(3000);
                    break;
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
    }

    async extractByInteraction() {
        console.log('方法3: 通过交互获取内容...');
        
        // 尝试点击可能的题目容器
        const clickableSelectors = [
            '.question-item',
            '.practice-item',
            '.exam-item',
            '.list-item',
            '[role="button"]',
            '.clickable'
        ];
        
        for (const selector of clickableSelectors) {
            try {
                const elements = await this.page.$$(selector);
                for (let i = 0; i < Math.min(elements.length, 5); i++) {
                    const element = elements[i];
                    await element.click();
                    await this.page.waitForTimeout(1000);
                    
                    // 检查是否有新内容出现
                    const newContent = await this.page.textContent('body');
                    if (this.isQuestionText(newContent)) {
                        this.questions.push({
                            method: 'interaction',
                            selector: selector,
                            index: i + 1,
                            text: newContent,
                            extractedAt: new Date().toISOString()
                        });
                    }
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
    }

    isQuestionText(text) {
        if (!text || text.length < 20) return false;
        
        const questionKeywords = [
            '题', '问', '选择', '判断', '填空',
            'A.', 'B.', 'C.', 'D.',
            '正确', '错误', '答案', '解析',
            '下列', '以下', '关于', '属于'
        ];
        
        return questionKeywords.some(keyword => text.includes(keyword));
    }

    extractFromAPIData(data, url) {
        console.log('从API数据中提取题目...');
        
        const searchForQuestions = (obj, path = '') => {
            if (typeof obj !== 'object' || obj === null) return;
            
            for (const [key, value] of Object.entries(obj)) {
                const currentPath = path ? `${path}.${key}` : key;
                
                if (typeof value === 'string' && this.isQuestionText(value)) {
                    this.questions.push({
                        method: 'api',
                        source: url,
                        path: currentPath,
                        text: value,
                        extractedAt: new Date().toISOString()
                    });
                } else if (Array.isArray(value)) {
                    value.forEach((item, index) => {
                        searchForQuestions(item, `${currentPath}[${index}]`);
                    });
                } else if (typeof value === 'object') {
                    searchForQuestions(value, currentPath);
                }
            }
        };
        
        searchForQuestions(data);
    }

    async saveResults() {
        // 去重
        const uniqueQuestions = this.removeDuplicates(this.questions);
        
        if (uniqueQuestions.length === 0) {
            console.log('未找到题目，保存调试信息...');
            
            const html = await this.page.content();
            fs.writeFileSync('debug_page.html', html, 'utf8');
            
            await this.page.screenshot({ 
                path: 'debug_screenshot.png', 
                fullPage: true 
            });
            
            console.log('调试信息已保存');
            return;
        }
        
        const result = {
            url: this.page.url(),
            timestamp: new Date().toISOString(),
            totalQuestions: uniqueQuestions.length,
            extractionMethods: [...new Set(uniqueQuestions.map(q => q.method))],
            questions: uniqueQuestions
        };
        
        // 保存详细JSON
        fs.writeFileSync('enhanced_questions.json', JSON.stringify(result, null, 2), 'utf8');
        
        // 保存可读格式
        let readableContent = `CISP网络安全知识竞赛练习题 - 增强版提取\n`;
        readableContent += `=`.repeat(60) + '\n';
        readableContent += `提取时间: ${new Date().toLocaleString()}\n`;
        readableContent += `题目总数: ${uniqueQuestions.length}\n`;
        readableContent += `提取方法: ${result.extractionMethods.join(', ')}\n\n`;
        
        uniqueQuestions.forEach((q, index) => {
            readableContent += `题目 ${index + 1} (${q.method})\n`;
            readableContent += `-`.repeat(40) + '\n';
            
            // 清理和格式化题目文本
            const cleanText = this.cleanQuestionText(q.text);
            readableContent += `${cleanText}\n\n`;
        });
        
        fs.writeFileSync('enhanced_questions.txt', readableContent, 'utf8');
        
        console.log(`成功提取 ${uniqueQuestions.length} 个题目`);
        console.log('详细数据已保存到 enhanced_questions.json');
        console.log('可读格式已保存到 enhanced_questions.txt');
    }

    cleanQuestionText(text) {
        // 清理题目文本，提取关键信息
        let cleaned = text.replace(/\s+/g, ' ').trim();
        
        // 尝试提取题目编号、题目内容、选项、答案和解析
        const parts = {
            number: '',
            question: '',
            options: [],
            answer: '',
            explanation: ''
        };
        
        // 提取题目编号
        const numberMatch = cleaned.match(/^(\d+)、/);
        if (numberMatch) {
            parts.number = numberMatch[1];
        }
        
        // 提取正确答案
        const answerMatch = cleaned.match(/正确答案：\s*([A-D])/);
        if (answerMatch) {
            parts.answer = answerMatch[1];
        }
        
        // 提取解析
        const explanationMatch = cleaned.match(/解析：(.+?)$/);
        if (explanationMatch) {
            parts.explanation = explanationMatch[1];
        }
        
        return cleaned;
    }

    removeDuplicates(questions) {
        const seen = new Set();
        return questions.filter(q => {
            const key = q.text.substring(0, 100);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.init();
            await this.navigateAndWait();
            await this.extractQuestions();
            await this.saveResults();
        } catch (error) {
            console.error('爬取过程中出现错误:', error);
        } finally {
            await this.close();
        }
    }
}

// 运行增强版爬虫
const scraper = new EnhancedCISPScraper();
scraper.run().catch(console.error);
