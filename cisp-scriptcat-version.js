// ==UserScript==
// @name         CISP自动答题助手 (脚本猫专用版)
// @namespace    http://tampermonkey.net/
// @version      3.1
// @description  专为脚本猫优化的CISP自动答题工具，智能遍历题目，识别已答状态，记录完整信息
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let totalQuestions = 0;

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  function createUI() {
    const panel = document.createElement('div');
    panel.style.cssText = `
      position: fixed; top: 100px; right: 30px; background: #fff;
      border: 2px solid #4CAF50; border-radius: 10px; padding: 15px;
      z-index: 9999; box-shadow: 0 0 15px rgba(0,0,0,0.3);
      min-width: 250px; font-family: Arial, sans-serif;
    `;
    panel.innerHTML = `
      <div style="font-weight:bold;color:#2E7D32;margin-bottom:10px">🤖 CISP答题助手</div>
      <button id="startBtn" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px">开始处理</button>
      <button id="stopBtn" style="width:100%;padding:8px;background:#f44336;color:white;border:none;border-radius:5px;cursor:pointer;display:none">停止</button>
      <div id="status" style="color:#555;font-size:14px;margin-bottom:5px">状态：未启动</div>
      <div id="progress" style="color:#666;font-size:12px;margin-bottom:5px">进度：0/0</div>
      <div id="current" style="color:#333;font-size:11px;background:#f5f5f5;padding:5px;border-radius:3px">当前：未开始</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startBtn').onclick = startProcess;
    document.getElementById('stopBtn').onclick = stopProcess;
  }

  function startProcess() {
    if (confirm("确认开始处理CISP题目？\n\n✅ 自动遍历所有题目\n✅ 识别已答/未答状态\n✅ 对未答题目随机选择\n✅ 记录所有信息并导出")) {
      document.getElementById('startBtn').style.display = 'none';
      document.getElementById('stopBtn').style.display = 'block';
      mainProcess();
    }
  }

  function stopProcess() {
    answering = false;
    updateStatus('已停止', `${records.length}/${totalQuestions}`, '用户停止');
    document.getElementById('startBtn').style.display = 'block';
    document.getElementById('stopBtn').style.display = 'none';
    if (records.length > 0) exportData();
  }

  function updateStatus(status, progress, current) {
    document.getElementById('status').innerText = status;
    document.getElementById('progress').innerText = progress;
    document.getElementById('current').innerText = current;
  }

  // 获取题目按钮
  function getQuestionButtons() {
    // 多种策略查找题目按钮
    let buttons = [];
    
    // 策略1: 查找所有数字按钮
    const allButtons = document.querySelectorAll('button, div[onclick], span[onclick]');
    buttons = Array.from(allButtons).filter(btn => {
      const text = btn.textContent.trim();
      return /^\d+$/.test(text) && parseInt(text) > 0 && parseInt(text) <= 200;
    });
    
    // 策略2: 如果没找到，查找左侧面板
    if (buttons.length === 0) {
      const leftPanels = document.querySelectorAll('.left-panel, .sidebar, [class*="left"]');
      for (const panel of leftPanels) {
        const elements = panel.querySelectorAll('*');
        const numElements = Array.from(elements).filter(el => {
          const text = el.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0;
        });
        if (numElements.length > 10) {
          buttons = numElements;
          break;
        }
      }
    }
    
    // 按数字排序
    buttons.sort((a, b) => parseInt(a.textContent.trim()) - parseInt(b.textContent.trim()));
    console.log(`找到 ${buttons.length} 个题目按钮`);
    return buttons;
  }

  // 检查是否已答
  function isAnswered(button) {
    const style = window.getComputedStyle(button);
    const bgColor = style.backgroundColor;
    const color = style.color;
    const borderColor = style.borderColor;
    
    // 检查特殊样式
    const hasSpecialBg = bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent' && bgColor !== 'rgb(255, 255, 255)';
    const hasSpecialColor = color !== 'rgb(0, 0, 0)';
    const hasRedBorder = borderColor && borderColor.includes('rgb(255');
    
    return hasSpecialBg || hasSpecialColor || hasRedBorder || button.disabled;
  }

  // 获取题目数据
  function getQuestionData() {
    let question = '';
    let options = [];
    
    // 获取题目
    const qSelectors = ['.questionStem', '.question-content', '.question-text'];
    for (const sel of qSelectors) {
      const el = document.querySelector(sel);
      if (el && el.innerText.trim().length > 10) {
        question = el.innerText.trim();
        break;
      }
    }
    
    // 获取选项
    const oSelectors = ['.answerItem', '.option-item', '.answer-option'];
    for (const sel of oSelectors) {
      const els = document.querySelectorAll(sel);
      if (els.length >= 2) {
        options = Array.from(els).map(el => el.innerText.trim());
        break;
      }
    }
    
    return { question, options };
  }

  // 获取用户答案
  function getUserAnswer() {
    const selectors = ['.answerItem.selected', '.option-item.selected', '.answer-option.selected'];
    for (const sel of selectors) {
      const el = document.querySelector(sel);
      if (el) return el.innerText.trim();
    }
    return '';
  }

  // 获取正确答案和解析
  function getCorrectInfo() {
    let correct = '';
    let explanation = '';
    
    // 正确答案
    const correctSels = ['.isCorrect', '.correct', '[class*="correct"]'];
    for (const sel of correctSels) {
      const el = document.querySelector(sel);
      if (el) {
        const match = el.innerText.match(/[A-D]/);
        if (match) {
          correct = match[0];
          break;
        }
      }
    }
    
    // 解析
    const expSels = ['.question-analysis', '.parse-info', '.explanation'];
    for (const sel of expSels) {
      const el = document.querySelector(sel);
      if (el) {
        let text = el.innerText.trim();
        text = text.replace(/^(解析|分析)[:：]?\s*/, '');
        if (text.length > 10) {
          explanation = text;
          break;
        }
      }
    }
    
    return { correct, explanation };
  }

  // 随机选择选项
  async function selectOption() {
    const options = document.querySelectorAll('.answerItem');
    if (options.length === 0) return false;
    
    const selected = document.querySelector('.answerItem.selected');
    if (selected) return true;
    
    const index = Math.floor(Math.random() * options.length);
    options[index].click();
    await delay(800);
    return true;
  }

  // 主处理流程
  async function mainProcess() {
    answering = true;
    records = [];
    
    try {
      const questionButtons = getQuestionButtons();
      totalQuestions = questionButtons.length;
      
      if (totalQuestions === 0) {
        updateStatus('错误：未找到题目', '0/0', '请检查页面');
        return;
      }
      
      updateStatus('开始处理', `0/${totalQuestions}`, `找到 ${totalQuestions} 道题`);
      await delay(2000);
      
      for (let i = 0; i < questionButtons.length && answering; i++) {
        const button = questionButtons[i];
        const num = button.textContent.trim();
        
        updateStatus('处理中', `${i + 1}/${totalQuestions}`, `题目 ${num}`);
        
        try {
          // 点击题目
          button.click();
          await delay(1500);
          
          // 获取数据
          const { question, options } = getQuestionData();
          if (!question || options.length === 0) {
            console.log(`题目 ${num} 数据获取失败`);
            continue;
          }
          
          const wasAnswered = isAnswered(button);
          let userAnswer = getUserAnswer();
          let attempted = false;
          
          // 如果未答，尝试选择
          if (!userAnswer && !wasAnswered) {
            attempted = await selectOption();
            if (attempted) {
              await delay(1000);
              userAnswer = getUserAnswer();
            }
          }
          
          // 获取正确答案和解析
          await delay(500);
          const { correct, explanation } = getCorrectInfo();
          
          // 记录
          records.push({
            index: parseInt(num),
            question,
            options,
            userAnswer: userAnswer || '未获取',
            correctAnswer: correct || '未知',
            explanation: explanation || '暂无',
            wasAnswered,
            attempted,
            time: new Date().toLocaleString()
          });
          
          const status = wasAnswered ? '(已答)' : (attempted ? '(新答)' : '(记录)');
          updateStatus('处理中', `${i + 1}/${totalQuestions}`, `题目 ${num} ${status}`);
          
        } catch (error) {
          console.error(`题目 ${num} 处理错误:`, error);
        }
        
        await delay(300);
      }
      
      updateStatus('完成', `${records.length}/${totalQuestions}`, '正在导出...');
      await delay(1000);
      exportData();
      
    } catch (error) {
      console.error('处理错误:', error);
      updateStatus('出错', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startBtn').style.display = 'block';
      document.getElementById('stopBtn').style.display = 'none';
    }
  }

  // 导出数据
  function exportData() {
    if (records.length === 0) {
      alert('没有数据可导出');
      return;
    }
    
    const wasAnsweredCount = records.filter(r => r.wasAnswered).length;
    const newAnsweredCount = records.filter(r => r.attempted).length;
    
    const header = `CISP答题记录 - 脚本猫版
生成时间：${new Date().toLocaleString()}
总题数：${records.length}
原已答：${wasAnsweredCount}
新答题：${newAnsweredCount}
${'='.repeat(50)}

`;
    
    const content = records.map(rec => {
      const status = rec.wasAnswered ? '🔄 原已答' : (rec.attempted ? '✨ 新答题' : '📝 仅记录');
      return `【题目 ${rec.index}】${status}
${rec.question}

${rec.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}

你的答案：${rec.userAnswer}
正确答案：${rec.correctAnswer}
解析：${rec.explanation}

${'='.repeat(50)}
`;
    }).join('\n');
    
    const blob = new Blob([header + content], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    link.download = `CISP记录_${new Date().toISOString().slice(0,10)}_${records.length}题.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();
    
    updateStatus('导出完成', `${records.length}/${totalQuestions}`, '文件已下载');
    
    setTimeout(() => {
      alert(`🎉 处理完成！\n\n总题数：${records.length}\n原已答：${wasAnsweredCount}\n新答题：${newAnsweredCount}\n\n记录已导出到下载文件夹`);
    }, 500);
  }

  // 初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => setTimeout(createUI, 1000));
  } else {
    setTimeout(createUI, 1000);
  }

})();
