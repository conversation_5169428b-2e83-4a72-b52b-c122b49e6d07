// ==UserScript==
// @name         CISP自动答题助手 (脚本猫专用版)
// @namespace    http://tampermonkey.net/
// @version      3.1
// @description  专为脚本猫优化的CISP自动答题工具，智能遍历题目，识别已答状态，记录完整信息
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let totalQuestions = 0;

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  function createUI() {
    const panel = document.createElement('div');
    panel.style.cssText = `
      position: fixed; top: 100px; right: 30px; background: #fff;
      border: 2px solid #4CAF50; border-radius: 10px; padding: 15px;
      z-index: 9999; box-shadow: 0 0 15px rgba(0,0,0,0.3);
      min-width: 250px; font-family: Arial, sans-serif;
    `;
    panel.innerHTML = `
      <div style="font-weight:bold;color:#2E7D32;margin-bottom:10px">🤖 CISP答题助手</div>
      <button id="startBtn" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px">开始处理</button>
      <button id="stopBtn" style="width:100%;padding:8px;background:#f44336;color:white;border:none;border-radius:5px;cursor:pointer;display:none">停止</button>
      <button id="debugBtn" style="width:100%;padding:6px;background:#2196F3;color:white;border:none;border-radius:5px;cursor:pointer;margin-bottom:5px;font-size:12px">调试分析</button>
      <div id="status" style="color:#555;font-size:14px;margin-bottom:5px">状态：未启动</div>
      <div id="progress" style="color:#666;font-size:12px;margin-bottom:5px">进度：0/0</div>
      <div id="current" style="color:#333;font-size:11px;background:#f5f5f5;padding:5px;border-radius:3px">当前：未开始</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startBtn').onclick = startProcess;
    document.getElementById('stopBtn').onclick = stopProcess;
    document.getElementById('debugBtn').onclick = debugAnalysis;
  }

  function startProcess() {
    if (confirm("确认开始处理CISP题目？\n\n✅ 自动遍历所有题目\n✅ 识别已答/未答状态\n✅ 对未答题目随机选择\n✅ 记录所有信息并导出")) {
      document.getElementById('startBtn').style.display = 'none';
      document.getElementById('stopBtn').style.display = 'block';
      mainProcess();
    }
  }

  function stopProcess() {
    answering = false;
    updateStatus('已停止', `${records.length}/${totalQuestions}`, '用户停止');
    document.getElementById('startBtn').style.display = 'block';
    document.getElementById('stopBtn').style.display = 'none';

    // 询问是否导出记录
    if (records.length > 0) {
      const shouldExport = confirm(`已停止处理，共处理了 ${records.length} 道题目。\n\n是否导出答题记录？\n\n记录包含：\n• 题目信息\n• 答案信息  \n• 正确答案\n• 解析内容`);
      if (shouldExport) {
        exportData();
      }
    } else {
      alert('没有处理任何题目，无记录可导出。');
    }
  }

  function updateStatus(status, progress, current) {
    document.getElementById('status').innerText = status;
    document.getElementById('progress').innerText = progress;
    document.getElementById('current').innerText = current;
  }

  // 获取题目按钮
  function getQuestionButtons() {
    // 多种策略查找题目按钮
    let buttons = [];
    
    // 策略1: 查找所有数字按钮
    const allButtons = document.querySelectorAll('button, div[onclick], span[onclick]');
    buttons = Array.from(allButtons).filter(btn => {
      const text = btn.textContent.trim();
      return /^\d+$/.test(text) && parseInt(text) > 0 && parseInt(text) <= 200;
    });
    
    // 策略2: 如果没找到，查找左侧面板
    if (buttons.length === 0) {
      const leftPanels = document.querySelectorAll('.left-panel, .sidebar, [class*="left"]');
      for (const panel of leftPanels) {
        const elements = panel.querySelectorAll('*');
        const numElements = Array.from(elements).filter(el => {
          const text = el.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0;
        });
        if (numElements.length > 10) {
          buttons = numElements;
          break;
        }
      }
    }
    
    // 按数字排序
    buttons.sort((a, b) => parseInt(a.textContent.trim()) - parseInt(b.textContent.trim()));
    console.log(`找到 ${buttons.length} 个题目按钮`);
    return buttons;
  }

  // 检查是否已答
  function isAnswered(button) {
    const style = window.getComputedStyle(button);
    const bgColor = style.backgroundColor;
    const color = style.color;
    const borderColor = style.borderColor;
    
    // 检查特殊样式
    const hasSpecialBg = bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent' && bgColor !== 'rgb(255, 255, 255)';
    const hasSpecialColor = color !== 'rgb(0, 0, 0)';
    const hasRedBorder = borderColor && borderColor.includes('rgb(255');
    
    return hasSpecialBg || hasSpecialColor || hasRedBorder || button.disabled;
  }

  // 获取题目数据
  function getQuestionData() {
    let question = '';
    let options = [];
    
    // 获取题目
    const qSelectors = ['.questionStem', '.question-content', '.question-text'];
    for (const sel of qSelectors) {
      const el = document.querySelector(sel);
      if (el && el.innerText.trim().length > 10) {
        question = el.innerText.trim();
        break;
      }
    }
    
    // 获取选项
    const oSelectors = ['.answerItem', '.option-item', '.answer-option'];
    for (const sel of oSelectors) {
      const els = document.querySelectorAll(sel);
      if (els.length >= 2) {
        options = Array.from(els).map(el => el.innerText.trim());
        break;
      }
    }
    
    return { question, options };
  }

  // 获取用户答案 - 增强版
  function getUserAnswer() {
    // 多种方式获取用户选择的答案
    const strategies = [
      // 策略1: 查找选中的选项元素
      () => {
        const selectors = [
          '.answerItem.selected',
          '.option-item.selected',
          '.answer-option.selected',
          '.choice-item.selected',
          '[class*="answer"][class*="selected"]',
          '[class*="option"][class*="selected"]'
        ];

        for (const sel of selectors) {
          const el = document.querySelector(sel);
          if (el) {
            console.log(`通过选择器 ${sel} 找到答案: ${el.innerText.trim().substring(0, 20)}...`);
            return el.innerText.trim();
          }
        }
        return '';
      },

      // 策略2: 查找选中的radio按钮
      () => {
        const checkedRadio = document.querySelector('input[type="radio"]:checked');
        if (checkedRadio) {
          // 尝试找到对应的标签文本
          const label = document.querySelector(`label[for="${checkedRadio.id}"]`) ||
                       checkedRadio.closest('label') ||
                       checkedRadio.parentElement;
          if (label) {
            console.log(`通过radio按钮找到答案: ${label.innerText.trim().substring(0, 20)}...`);
            return label.innerText.trim();
          }
          return checkedRadio.value || '已选择';
        }
        return '';
      },

      // 策略3: 查找高亮或特殊样式的选项
      () => {
        const allOptions = document.querySelectorAll('.answerItem, .option-item, .answer-option, [class*="answer"]');
        for (const option of allOptions) {
          const style = window.getComputedStyle(option);
          const bgColor = style.backgroundColor;
          const color = style.color;

          // 检查是否有特殊的背景色或文字色（表示选中）
          const hasSpecialBg = bgColor !== 'rgba(0, 0, 0, 0)' &&
                              bgColor !== 'transparent' &&
                              bgColor !== 'rgb(255, 255, 255)';
          const hasSpecialColor = color !== 'rgb(0, 0, 0)';

          if (hasSpecialBg || hasSpecialColor) {
            console.log(`通过样式检测找到答案: ${option.innerText.trim().substring(0, 20)}...`);
            return option.innerText.trim();
          }
        }
        return '';
      },

      // 策略4: 查找包含特定类名的元素
      () => {
        const activeElements = document.querySelectorAll('[class*="active"], [class*="current"], [class*="checked"]');
        for (const el of activeElements) {
          const text = el.innerText.trim();
          if (text && text.match(/^[A-D]\./) && text.length > 3) {
            console.log(`通过active类名找到答案: ${text.substring(0, 20)}...`);
            return text;
          }
        }
        return '';
      }
    ];

    // 依次尝试各种策略
    for (let i = 0; i < strategies.length; i++) {
      const result = strategies[i]();
      if (result) {
        console.log(`策略 ${i + 1} 成功获取到用户答案`);
        return result;
      }
    }

    console.log('未能获取到用户答案');
    return '';
  }

  // 获取正确答案和解析
  function getCorrectInfo() {
    let correct = '';
    let explanation = '';
    
    // 正确答案
    const correctSels = ['.isCorrect', '.correct', '[class*="correct"]'];
    for (const sel of correctSels) {
      const el = document.querySelector(sel);
      if (el) {
        const match = el.innerText.match(/[A-D]/);
        if (match) {
          correct = match[0];
          break;
        }
      }
    }
    
    // 解析
    const expSels = ['.question-analysis', '.parse-info', '.explanation'];
    for (const sel of expSels) {
      const el = document.querySelector(sel);
      if (el) {
        let text = el.innerText.trim();
        text = text.replace(/^(解析|分析)[:：]?\s*/, '');
        if (text.length > 10) {
          explanation = text;
          break;
        }
      }
    }
    
    return { correct, explanation };
  }

  // 随机选择选项 - 增强版
  async function selectOption() {
    // 多种选择器尝试找到选项
    const selectors = [
      '.answerItem',
      '.option-item',
      '.answer-option',
      '.choice-item',
      '[class*="answer"]',
      'input[type="radio"]',
      'label[for*="answer"]'
    ];

    let options = [];
    for (const selector of selectors) {
      options = document.querySelectorAll(selector);
      if (options.length >= 2) {
        console.log(`使用选择器 ${selector} 找到 ${options.length} 个选项`);
        break;
      }
    }

    if (options.length === 0) {
      console.log('未找到可选择的选项');
      return false;
    }

    // 检查是否已经选择
    const selectedSelectors = [
      '.answerItem.selected',
      '.option-item.selected',
      '.answer-option.selected',
      '.choice-item.selected',
      'input[type="radio"]:checked',
      '[class*="selected"]'
    ];

    for (const selector of selectedSelectors) {
      const selected = document.querySelector(selector);
      if (selected) {
        console.log('检测到已选择的选项，跳过随机选择');
        return true;
      }
    }

    // 随机选择一个选项
    const index = Math.floor(Math.random() * options.length);
    const selectedOption = options[index];

    console.log(`随机选择第 ${index + 1} 个选项: ${selectedOption.textContent.trim().substring(0, 20)}...`);

    // 尝试多种点击方式
    try {
      // 方式1: 直接点击
      selectedOption.click();
      await delay(500);

      // 方式2: 如果是radio input，确保选中
      const radio = selectedOption.querySelector('input[type="radio"]') ||
                   (selectedOption.tagName === 'INPUT' && selectedOption.type === 'radio' ? selectedOption : null);
      if (radio) {
        radio.checked = true;
        radio.dispatchEvent(new Event('change', { bubbles: true }));
      }

      // 方式3: 触发事件
      selectedOption.dispatchEvent(new Event('click', { bubbles: true }));
      selectedOption.dispatchEvent(new Event('change', { bubbles: true }));

      await delay(800);

      // 验证是否选择成功
      const nowSelected = document.querySelector('.answerItem.selected, .option-item.selected, input[type="radio"]:checked');
      if (nowSelected) {
        console.log('选项选择成功');
        return true;
      } else {
        console.log('选项选择可能失败，但继续处理');
        return true; // 即使检测不到选中状态，也认为尝试过了
      }

    } catch (error) {
      console.error('选择选项时出错:', error);
      return false;
    }
  }

  // 主处理流程
  async function mainProcess() {
    answering = true;
    records = [];
    
    try {
      const questionButtons = getQuestionButtons();
      totalQuestions = questionButtons.length;
      
      if (totalQuestions === 0) {
        updateStatus('错误：未找到题目', '0/0', '请检查页面');
        return;
      }
      
      updateStatus('开始处理', `0/${totalQuestions}`, `找到 ${totalQuestions} 道题`);
      await delay(2000);
      
      for (let i = 0; i < questionButtons.length && answering; i++) {
        const button = questionButtons[i];
        const num = button.textContent.trim();
        
        updateStatus('处理中', `${i + 1}/${totalQuestions}`, `题目 ${num}`);
        
        try {
          // 点击题目
          button.click();
          await delay(1500);
          
          // 获取数据
          const { question, options } = getQuestionData();
          if (!question || options.length === 0) {
            console.log(`题目 ${num} 数据获取失败`);
            continue;
          }
          
          const wasAnswered = isAnswered(button);
          let userAnswer = getUserAnswer();
          let attempted = false;

          console.log(`题目 ${num} - 已答状态: ${wasAnswered}, 当前答案: ${userAnswer || '无'}`);

          // 处理答题逻辑
          if (!userAnswer && !wasAnswered) {
            // 未答且无选择，尝试随机选择
            console.log(`题目 ${num} 未答，尝试随机选择选项`);
            updateStatus('处理中', `${i + 1}/${totalQuestions}`, `题目 ${num} - 选择答案`);

            attempted = await selectOption();
            if (attempted) {
              await delay(1500); // 等待页面响应
              userAnswer = getUserAnswer();
              console.log(`选择后获取到答案: ${userAnswer || '未获取到'}`);
            }
          } else if (wasAnswered && !userAnswer) {
            // 已答但无法获取答案，尝试多次获取
            console.log(`题目 ${num} 已答但未获取到答案，尝试重新获取`);
            for (let retry = 0; retry < 3; retry++) {
              await delay(500);
              userAnswer = getUserAnswer();
              if (userAnswer) {
                console.log(`重试 ${retry + 1} 次后获取到答案: ${userAnswer}`);
                break;
              }
            }
          }
          
          // 获取正确答案和解析
          await delay(500);
          const { correct, explanation } = getCorrectInfo();
          
          // 记录
          records.push({
            index: parseInt(num),
            question,
            options,
            userAnswer: userAnswer || '未获取',
            correctAnswer: correct || '未知',
            explanation: explanation || '暂无',
            wasAnswered,
            attempted,
            time: new Date().toLocaleString()
          });
          
          const status = wasAnswered ? '(已答)' : (attempted ? '(新答)' : '(记录)');
          updateStatus('处理中', `${i + 1}/${totalQuestions}`, `题目 ${num} ${status}`);
          
        } catch (error) {
          console.error(`题目 ${num} 处理错误:`, error);
        }
        
        await delay(300);
      }
      
      updateStatus('完成', `${records.length}/${totalQuestions}`, '正在导出...');
      await delay(1000);
      exportData();
      
    } catch (error) {
      console.error('处理错误:', error);
      updateStatus('出错', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startBtn').style.display = 'block';
      document.getElementById('stopBtn').style.display = 'none';
    }
  }

  // 导出数据
  function exportData() {
    if (records.length === 0) {
      alert('没有数据可导出');
      return;
    }
    
    const wasAnsweredCount = records.filter(r => r.wasAnswered).length;
    const newAnsweredCount = records.filter(r => r.attempted).length;
    
    const header = `CISP答题记录 - 脚本猫版
生成时间：${new Date().toLocaleString()}
总题数：${records.length}
原已答：${wasAnsweredCount}
新答题：${newAnsweredCount}
${'='.repeat(50)}

`;
    
    const content = records.map(rec => {
      const status = rec.wasAnswered ? '🔄 原已答' : (rec.attempted ? '✨ 新答题' : '📝 仅记录');
      return `【题目 ${rec.index}】${status}
${rec.question}

${rec.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}

你的答案：${rec.userAnswer}
正确答案：${rec.correctAnswer}
解析：${rec.explanation}

${'='.repeat(50)}
`;
    }).join('\n');
    
    const blob = new Blob([header + content], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    link.download = `CISP记录_${new Date().toISOString().slice(0,10)}_${records.length}题.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();
    
    updateStatus('导出完成', `${records.length}/${totalQuestions}`, '文件已下载');
    
    setTimeout(() => {
      alert(`🎉 处理完成！\n\n总题数：${records.length}\n原已答：${wasAnsweredCount}\n新答题：${newAnsweredCount}\n\n记录已导出到下载文件夹`);
    }, 500);
  }

  // 调试分析功能
  function debugAnalysis() {
    console.log('=== CISP页面调试分析 ===');
    updateStatus('调试分析', '分析中', '请查看控制台');

    // 分析题目按钮
    console.log('\n1. 题目按钮分析:');
    const buttons = getQuestionButtons();
    console.log(`找到 ${buttons.length} 个题目按钮`);
    if (buttons.length > 0) {
      const firstBtn = buttons[0];
      console.log(`第一个按钮: "${firstBtn.textContent.trim()}"`);
      console.log(`按钮类名: ${firstBtn.className}`);
      console.log(`是否已答: ${isAnswered(firstBtn)}`);
    }

    // 分析当前题目
    console.log('\n2. 当前题目分析:');
    const { question, options } = getQuestionData();
    console.log(`题目: ${question.substring(0, 100)}...`);
    console.log(`选项数量: ${options.length}`);
    options.forEach((opt, i) => {
      console.log(`  ${String.fromCharCode(65 + i)}. ${opt.substring(0, 50)}...`);
    });

    // 分析用户答案
    console.log('\n3. 用户答案分析:');
    const userAnswer = getUserAnswer();
    console.log(`当前答案: ${userAnswer || '未选择'}`);

    // 分析选项元素
    console.log('\n4. 选项元素分析:');
    const optionElements = document.querySelectorAll('.answerItem');
    console.log(`找到 ${optionElements.length} 个 .answerItem 元素`);
    optionElements.forEach((el, i) => {
      const style = window.getComputedStyle(el);
      console.log(`选项 ${i + 1}: ${el.textContent.trim().substring(0, 30)}...`);
      console.log(`  类名: ${el.className}`);
      console.log(`  背景: ${style.backgroundColor}`);
      console.log(`  文字: ${style.color}`);
    });

    // 分析正确答案和解析
    console.log('\n5. 正确答案和解析分析:');
    const { correct, explanation } = getCorrectInfo();
    console.log(`正确答案: ${correct || '未找到'}`);
    console.log(`解析: ${explanation ? explanation.substring(0, 100) + '...' : '未找到'}`);

    // 分析页面结构
    console.log('\n6. 页面结构分析:');
    console.log(`页面标题: ${document.title}`);
    console.log(`URL: ${window.location.href}`);

    const allButtons = document.querySelectorAll('button');
    console.log(`页面总按钮数: ${allButtons.length}`);

    const allInputs = document.querySelectorAll('input');
    console.log(`页面总输入框数: ${allInputs.length}`);

    const radioInputs = document.querySelectorAll('input[type="radio"]');
    console.log(`单选按钮数: ${radioInputs.length}`);

    updateStatus('调试完成', '分析完成', '请查看控制台输出');

    alert('调试分析完成！\n\n请打开浏览器控制台（F12）查看详细分析结果。\n\n分析内容包括：\n• 题目按钮识别\n• 当前题目内容\n• 选项元素结构\n• 答案获取状态\n• 页面结构信息');
  }

  // 初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => setTimeout(createUI, 1000));
  } else {
    setTimeout(createUI, 1000);
  }

})();
