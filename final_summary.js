const fs = require('fs');

class FinalSummary {
    constructor() {
        this.allQuestions = [];
        this.summary = {
            totalExtracted: 0,
            validQuestions: 0,
            extractionMethods: [],
            challenges: [],
            recommendations: []
        };
    }

    generateFinalReport() {
        console.log('生成最终爬取报告...');
        
        // 收集所有已提取的题目
        this.collectAllQuestions();
        
        // 分析爬取结果
        this.analyzeResults();
        
        // 生成最终报告
        this.createFinalReport();
        
        console.log('最终报告生成完成！');
    }

    collectAllQuestions() {
        const files = [
            'questions_detailed.json',
            'enhanced_questions.json',
            'parsed_questions.json'
        ];
        
        files.forEach(filename => {
            try {
                if (fs.existsSync(filename)) {
                    const data = JSON.parse(fs.readFileSync(filename, 'utf8'));
                    
                    if (data.questions && Array.isArray(data.questions)) {
                        this.allQuestions = this.allQuestions.concat(data.questions);
                        console.log(`从 ${filename} 收集到 ${data.questions.length} 个题目`);
                    }
                }
            } catch (error) {
                console.log(`读取 ${filename} 失败:`, error.message);
            }
        });
        
        // 去重
        this.allQuestions = this.removeDuplicates(this.allQuestions);
        console.log(`去重后共有 ${this.allQuestions.length} 个有效题目`);
    }

    removeDuplicates(questions) {
        const seen = new Set();
        return questions.filter(q => {
            const text = q.text || q.content || '';
            const key = text.substring(0, 50);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    analyzeResults() {
        this.summary.totalExtracted = this.allQuestions.length;
        this.summary.validQuestions = this.allQuestions.filter(q => 
            (q.text && q.text.length > 30) || (q.content && q.content.length > 10)
        ).length;
        
        // 分析提取方法
        const methods = this.allQuestions.map(q => q.method).filter(Boolean);
        this.summary.extractionMethods = [...new Set(methods)];
        
        // 分析遇到的挑战
        this.summary.challenges = [
            '网站需要登录才能访问完整内容',
            '页面使用JavaScript动态加载内容',
            '大部分API接口需要身份验证',
            '题目内容分散在多个DOM元素中',
            '页面可能有反爬虫机制'
        ];
        
        // 提供改进建议
        this.summary.recommendations = [
            '手动登录后再运行爬虫脚本',
            '使用浏览器扩展或用户脚本获取更多内容',
            '分析网站的API接口，直接调用数据接口',
            '使用更长的等待时间确保页面完全加载',
            '考虑使用代理或更换User-Agent避免检测'
        ];
    }

    createFinalReport() {
        const report = {
            title: 'CISP网络安全知识竞赛题目爬取最终报告',
            timestamp: new Date().toISOString(),
            summary: this.summary,
            extractedQuestions: this.allQuestions,
            technicalDetails: this.getTechnicalDetails()
        };
        
        // 保存JSON格式报告
        fs.writeFileSync('final_report.json', JSON.stringify(report, null, 2), 'utf8');
        
        // 生成可读格式报告
        this.createReadableReport(report);
        
        // 生成Excel兼容的CSV
        this.createExcelCSV();
    }

    createReadableReport(report) {
        let content = `CISP网络安全知识竞赛题目爬取最终报告\n`;
        content += `${'='.repeat(60)}\n`;
        content += `生成时间: ${new Date().toLocaleString()}\n`;
        content += `目标网址: https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN\n\n`;
        
        content += `爬取结果摘要:\n`;
        content += `${'-'.repeat(30)}\n`;
        content += `总提取条目: ${report.summary.totalExtracted}\n`;
        content += `有效题目: ${report.summary.validQuestions}\n`;
        content += `提取方法: ${report.summary.extractionMethods.join(', ')}\n\n`;
        
        content += `遇到的挑战:\n`;
        content += `${'-'.repeat(30)}\n`;
        report.summary.challenges.forEach((challenge, index) => {
            content += `${index + 1}. ${challenge}\n`;
        });
        content += `\n`;
        
        content += `改进建议:\n`;
        content += `${'-'.repeat(30)}\n`;
        report.summary.recommendations.forEach((rec, index) => {
            content += `${index + 1}. ${rec}\n`;
        });
        content += `\n`;
        
        if (this.allQuestions.length > 0) {
            content += `提取到的题目详情:\n`;
            content += `${'-'.repeat(30)}\n`;
            
            this.allQuestions.forEach((q, index) => {
                content += `\n【题目 ${index + 1}】\n`;
                
                if (q.number) {
                    content += `编号: ${q.number}\n`;
                }
                
                const questionText = q.content || q.text || '';
                if (questionText) {
                    // 清理和格式化题目文本
                    const cleanText = this.cleanText(questionText);
                    content += `题目: ${cleanText}\n`;
                }
                
                if (q.options && q.options.length > 0) {
                    content += `选项:\n`;
                    q.options.forEach(option => {
                        content += `  ${option.letter}. ${option.text}\n`;
                    });
                }
                
                if (q.correctAnswer) {
                    content += `正确答案: ${q.correctAnswer}\n`;
                }
                
                if (q.explanation) {
                    content += `解析: ${q.explanation}\n`;
                }
                
                content += `提取方法: ${q.method || '未知'}\n`;
                content += `${'-'.repeat(40)}\n`;
            });
        }
        
        content += `\n技术实现说明:\n`;
        content += `${'-'.repeat(30)}\n`;
        content += `使用工具: Playwright (无头浏览器)\n`;
        content += `编程语言: Node.js\n`;
        content += `提取策略: CSS选择器 + 文本搜索 + API拦截\n`;
        content += `数据格式: JSON, CSV, TXT\n\n`;
        
        content += `文件说明:\n`;
        content += `${'-'.repeat(30)}\n`;
        content += `final_report.json - 完整的JSON格式报告\n`;
        content += `final_report.txt - 本可读格式报告\n`;
        content += `final_questions.csv - Excel兼容的题目数据\n`;
        content += `parsed_questions.* - 解析后的题目数据\n`;
        content += `questions_detailed.json - 原始提取数据\n`;
        content += `page_screenshot.png - 页面截图\n`;
        content += `README.md - 使用说明\n\n`;
        
        fs.writeFileSync('final_report.txt', content, 'utf8');
    }

    createExcelCSV() {
        if (this.allQuestions.length === 0) return;
        
        // 添加BOM以支持Excel中文显示
        let csvContent = '\uFEFF';
        csvContent += '题目编号,题目内容,选项A,选项B,选项C,选项D,正确答案,用户答案,回答状态,解析,提取方法\n';
        
        this.allQuestions.forEach(q => {
            const row = [
                q.number || '',
                `"${this.cleanText(q.content || q.text || '').replace(/"/g, '""')}"`,
                `"${this.getOptionText(q.options, 'A')}"`,
                `"${this.getOptionText(q.options, 'B')}"`,
                `"${this.getOptionText(q.options, 'C')}"`,
                `"${this.getOptionText(q.options, 'D')}"`,
                q.correctAnswer || '',
                q.userAnswer || '',
                q.status || '',
                `"${(q.explanation || '').replace(/"/g, '""')}"`,
                q.method || '未知'
            ];
            
            csvContent += row.join(',') + '\n';
        });
        
        fs.writeFileSync('final_questions.csv', csvContent, 'utf8');
    }

    getOptionText(options, letter) {
        if (!options || !Array.isArray(options)) return '';
        const option = options.find(opt => opt.letter === letter);
        return option ? option.text.replace(/"/g, '""') : '';
    }

    cleanText(text) {
        if (!text) return '';
        return text.replace(/\s+/g, ' ').trim();
    }

    getTechnicalDetails() {
        return {
            tools: ['Playwright', 'Node.js', 'JavaScript'],
            strategies: ['CSS Selectors', 'Text Search', 'API Interception'],
            challenges: ['Login Required', 'Dynamic Content', 'Anti-Scraping'],
            outputFormats: ['JSON', 'CSV', 'TXT'],
            files: [
                'final_report.json',
                'final_report.txt', 
                'final_questions.csv',
                'parsed_questions.json',
                'questions_detailed.json'
            ]
        };
    }

    run() {
        this.generateFinalReport();
        
        console.log('\n爬取任务完成！');
        console.log('='.repeat(50));
        console.log(`成功提取题目数量: ${this.summary.validQuestions}`);
        console.log(`生成文件:`);
        console.log('- final_report.txt (最终报告)');
        console.log('- final_questions.csv (Excel格式题目)');
        console.log('- final_report.json (完整数据)');
        console.log('\n建议: 如需获取更多题目，请手动登录后重新运行爬虫。');
    }
}

// 运行最终总结
const summary = new FinalSummary();
summary.run();
