const fs = require('fs');

class QuestionParser {
    constructor() {
        this.questions = [];
    }

    parseFromExistingData() {
        try {
            // 读取已有的题目数据
            const data = JSON.parse(fs.readFileSync('questions_detailed.json', 'utf8'));
            
            console.log('解析已有题目数据...');
            console.log(`找到 ${data.questions.length} 个原始题目条目`);
            
            data.questions.forEach((item, index) => {
                const parsed = this.parseQuestionText(item.text, index + 1);
                if (parsed) {
                    this.questions.push(parsed);
                }
            });
            
            console.log(`成功解析 ${this.questions.length} 个题目`);
            
        } catch (error) {
            console.error('读取题目数据失败:', error.message);
        }
    }

    parseQuestionText(text, originalIndex) {
        if (!text || text.length < 20) return null;
        
        const question = {
            originalIndex: originalIndex,
            number: '',
            content: '',
            options: [],
            correctAnswer: '',
            userAnswer: '',
            explanation: '',
            status: '',
            type: 'multiple_choice'
        };
        
        // 提取题目编号
        const numberMatch = text.match(/^(\d+)、/);
        if (numberMatch) {
            question.number = numberMatch[1];
        }
        
        // 提取题目内容（从编号后到第一个选项A.之前）
        const contentMatch = text.match(/\d+、(.+?)(?=A\.)/);
        if (contentMatch) {
            question.content = contentMatch[1].trim();
        }
        
        // 提取选项
        const optionPattern = /([A-D])\.(.*?)(?=[A-D]\.|回答|正确答案|$)/g;
        let optionMatch;
        while ((optionMatch = optionPattern.exec(text)) !== null) {
            const optionLetter = optionMatch[1];
            const optionText = optionMatch[2].trim();
            
            if (optionText) {
                question.options.push({
                    letter: optionLetter,
                    text: optionText
                });
            }
        }
        
        // 提取回答状态
        if (text.includes('回答正确')) {
            question.status = '正确';
        } else if (text.includes('回答错误')) {
            question.status = '错误';
        }
        
        // 提取正确答案
        const correctAnswerMatch = text.match(/正确答案：\s*([A-D])/);
        if (correctAnswerMatch) {
            question.correctAnswer = correctAnswerMatch[1];
        }
        
        // 提取用户答案
        const userAnswerMatch = text.match(/你的答案：\s*([A-D])/);
        if (userAnswerMatch) {
            question.userAnswer = userAnswerMatch[1];
        }
        
        // 提取解析
        const explanationMatch = text.match(/解析：(.+?)$/);
        if (explanationMatch) {
            question.explanation = explanationMatch[1].trim();
        }
        
        // 验证题目是否有效
        if (question.content && question.options.length >= 2) {
            return question;
        }
        
        return null;
    }

    generateFormattedOutput() {
        if (this.questions.length === 0) {
            console.log('没有有效的题目数据');
            return;
        }
        
        // 生成详细的JSON格式
        const detailedOutput = {
            title: 'CISP网络安全知识竞赛练习题',
            extractTime: new Date().toISOString(),
            totalQuestions: this.questions.length,
            questions: this.questions
        };
        
        fs.writeFileSync('parsed_questions.json', JSON.stringify(detailedOutput, null, 2), 'utf8');
        
        // 生成可读格式
        let readableOutput = `CISP网络安全知识竞赛练习题\n`;
        readableOutput += `${'='.repeat(50)}\n`;
        readableOutput += `解析时间: ${new Date().toLocaleString()}\n`;
        readableOutput += `题目总数: ${this.questions.length}\n\n`;
        
        this.questions.forEach((q, index) => {
            readableOutput += `【题目 ${index + 1}】\n`;
            if (q.number) {
                readableOutput += `编号: ${q.number}\n`;
            }
            readableOutput += `题目: ${q.content}\n\n`;
            
            if (q.options.length > 0) {
                readableOutput += `选项:\n`;
                q.options.forEach(option => {
                    readableOutput += `  ${option.letter}. ${option.text}\n`;
                });
                readableOutput += `\n`;
            }
            
            if (q.correctAnswer) {
                readableOutput += `正确答案: ${q.correctAnswer}\n`;
            }
            
            if (q.userAnswer) {
                readableOutput += `用户答案: ${q.userAnswer}\n`;
            }
            
            if (q.status) {
                readableOutput += `回答状态: ${q.status}\n`;
            }
            
            if (q.explanation) {
                readableOutput += `解析: ${q.explanation}\n`;
            }
            
            readableOutput += `${'-'.repeat(50)}\n\n`;
        });
        
        fs.writeFileSync('parsed_questions.txt', readableOutput, 'utf8');
        
        // 生成CSV格式
        this.generateCSV();
        
        console.log('格式化输出完成:');
        console.log('- parsed_questions.json (详细JSON格式)');
        console.log('- parsed_questions.txt (可读文本格式)');
        console.log('- parsed_questions.csv (CSV表格格式)');
    }

    generateCSV() {
        let csvContent = '题目编号,题目内容,选项A,选项B,选项C,选项D,正确答案,用户答案,回答状态,解析\n';
        
        this.questions.forEach(q => {
            const row = [
                q.number || '',
                `"${q.content.replace(/"/g, '""')}"`,
                `"${this.getOptionText(q.options, 'A')}"`,
                `"${this.getOptionText(q.options, 'B')}"`,
                `"${this.getOptionText(q.options, 'C')}"`,
                `"${this.getOptionText(q.options, 'D')}"`,
                q.correctAnswer || '',
                q.userAnswer || '',
                q.status || '',
                `"${(q.explanation || '').replace(/"/g, '""')}"`
            ];
            
            csvContent += row.join(',') + '\n';
        });
        
        fs.writeFileSync('parsed_questions.csv', csvContent, 'utf8');
    }

    getOptionText(options, letter) {
        const option = options.find(opt => opt.letter === letter);
        return option ? option.text.replace(/"/g, '""') : '';
    }

    generateSummary() {
        if (this.questions.length === 0) return;
        
        const summary = {
            totalQuestions: this.questions.length,
            correctAnswers: this.questions.filter(q => q.status === '正确').length,
            wrongAnswers: this.questions.filter(q => q.status === '错误').length,
            questionsWithExplanation: this.questions.filter(q => q.explanation).length,
            topicAreas: this.analyzeTopics()
        };
        
        summary.accuracy = summary.totalQuestions > 0 ? 
            (summary.correctAnswers / summary.totalQuestions * 100).toFixed(2) + '%' : '0%';
        
        console.log('\n题目统计摘要:');
        console.log(`总题目数: ${summary.totalQuestions}`);
        console.log(`正确答案: ${summary.correctAnswers}`);
        console.log(`错误答案: ${summary.wrongAnswers}`);
        console.log(`准确率: ${summary.accuracy}`);
        console.log(`有解析的题目: ${summary.questionsWithExplanation}`);
        
        fs.writeFileSync('question_summary.json', JSON.stringify(summary, null, 2), 'utf8');
    }

    analyzeTopics() {
        const topics = {};
        
        this.questions.forEach(q => {
            const content = q.content.toLowerCase();
            
            // 简单的主题分析
            if (content.includes('密码') || content.includes('加密') || content.includes('解密')) {
                topics['密码学'] = (topics['密码学'] || 0) + 1;
            }
            if (content.includes('网络') || content.includes('协议')) {
                topics['网络安全'] = (topics['网络安全'] || 0) + 1;
            }
            if (content.includes('攻击') || content.includes('漏洞')) {
                topics['安全攻防'] = (topics['安全攻防'] || 0) + 1;
            }
            if (content.includes('管理') || content.includes('策略')) {
                topics['安全管理'] = (topics['安全管理'] || 0) + 1;
            }
        });
        
        return topics;
    }

    run() {
        console.log('开始解析题目数据...');
        this.parseFromExistingData();
        this.generateFormattedOutput();
        this.generateSummary();
        console.log('题目解析完成！');
    }
}

// 运行解析器
const parser = new QuestionParser();
parser.run();
