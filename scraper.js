const { chromium } = require('playwright');
const fs = require('fs');

async function scrapeQuestions() {
    const browser = await chromium.launch({ 
        headless: false, // 设置为false以便调试
        slowMo: 1000 // 减慢操作速度
    });
    
    const page = await browser.newPage();
    
    try {
        console.log('正在访问目标网页...');
        await page.goto('https://www.cisp.cn/p/t_pc/pc_evaluation/practice_sheet/wb_course_68215ca6ca6ee_oiE0d5rN?product_id=course_2wz9KwPb5MUq4W63laK2HNYGSDU&task_id=pt_68215ca6e4cfe_kd4hq1pC', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log('等待页面完全加载...');
        await page.waitForTimeout(5000);
        
        // 尝试等待题目容器加载
        const possibleSelectors = [
            '.question-item',
            '.practice-question',
            '.exam-question',
            '.question-container',
            '.question-box',
            '[class*="question"]',
            '[class*="题目"]',
            '.item',
            '.list-item'
        ];
        
        let questionsFound = false;
        let questions = [];
        
        for (const selector of possibleSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 3000 });
                console.log(`找到题目容器: ${selector}`);
                questionsFound = true;
                break;
            } catch (e) {
                console.log(`未找到选择器: ${selector}`);
            }
        }
        
        if (!questionsFound) {
            console.log('尝试获取页面所有文本内容...');
            const pageContent = await page.content();
            fs.writeFileSync('page_content.html', pageContent, 'utf8');
            console.log('页面内容已保存到 page_content.html');
        }
        
        // 尝试多种方式获取题目信息
        console.log('尝试提取题目信息...');
        
        // 方法1: 通过常见的题目选择器
        const questionSelectors = [
            '.question-item',
            '.practice-question', 
            '.exam-question',
            '.question-container',
            '.question-box',
            '[data-question]',
            '.question',
            '.item'
        ];
        
        for (const selector of questionSelectors) {
            try {
                const elements = await page.$$(selector);
                if (elements.length > 0) {
                    console.log(`使用选择器 ${selector} 找到 ${elements.length} 个元素`);
                    
                    for (let i = 0; i < elements.length; i++) {
                        const element = elements[i];
                        const questionText = await element.textContent();
                        if (questionText && questionText.trim().length > 10) {
                            questions.push({
                                index: i + 1,
                                selector: selector,
                                content: questionText.trim()
                            });
                        }
                    }
                    
                    if (questions.length > 0) break;
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e.message);
            }
        }
        
        // 方法2: 如果没有找到题目，尝试获取所有包含"题"字的元素
        if (questions.length === 0) {
            console.log('尝试搜索包含"题"字的元素...');
            const textElements = await page.$$('*');
            
            for (let element of textElements) {
                try {
                    const text = await element.textContent();
                    if (text && (text.includes('题') || text.includes('问') || text.includes('选择') || text.includes('判断'))) {
                        const innerHTML = await element.innerHTML();
                        if (innerHTML.length > 20 && innerHTML.length < 1000) {
                            questions.push({
                                type: 'text_search',
                                content: text.trim()
                            });
                        }
                    }
                } catch (e) {
                    // 忽略错误，继续下一个元素
                }
            }
        }
        
        // 方法3: 尝试执行JavaScript获取动态内容
        console.log('尝试执行JavaScript获取动态内容...');
        const dynamicContent = await page.evaluate(() => {
            const results = [];
            
            // 查找所有可能包含题目的元素
            const allElements = document.querySelectorAll('*');
            
            allElements.forEach((element, index) => {
                const text = element.textContent || '';
                const tagName = element.tagName.toLowerCase();
                
                // 检查是否可能是题目
                if (text.length > 20 && text.length < 2000) {
                    if (text.includes('题') || text.includes('问') || 
                        text.includes('选择') || text.includes('判断') ||
                        text.includes('A.') || text.includes('B.') ||
                        text.includes('正确') || text.includes('错误')) {
                        
                        results.push({
                            index: index,
                            tagName: tagName,
                            className: element.className,
                            id: element.id,
                            text: text.trim()
                        });
                    }
                }
            });
            
            return results;
        });
        
        if (dynamicContent.length > 0) {
            console.log(`通过JavaScript找到 ${dynamicContent.length} 个可能的题目元素`);
            questions = questions.concat(dynamicContent);
        }
        
        // 保存结果
        if (questions.length > 0) {
            const result = {
                url: page.url(),
                timestamp: new Date().toISOString(),
                totalQuestions: questions.length,
                questions: questions
            };
            
            fs.writeFileSync('questions.json', JSON.stringify(result, null, 2), 'utf8');
            console.log(`成功提取 ${questions.length} 个题目，已保存到 questions.json`);
            
            // 同时保存为可读格式
            let readableContent = `网络安全知识竞赛练习题\n`;
            readableContent += `提取时间: ${new Date().toLocaleString()}\n`;
            readableContent += `题目总数: ${questions.length}\n\n`;
            
            questions.forEach((q, index) => {
                readableContent += `=== 题目 ${index + 1} ===\n`;
                readableContent += `${q.content}\n\n`;
            });
            
            fs.writeFileSync('questions.txt', readableContent, 'utf8');
            console.log('可读格式已保存到 questions.txt');
            
        } else {
            console.log('未找到题目内容，可能需要登录或页面结构已变化');
            
            // 保存页面截图用于调试
            await page.screenshot({ path: 'page_screenshot.png', fullPage: true });
            console.log('页面截图已保存到 page_screenshot.png');
        }
        
    } catch (error) {
        console.error('爬取过程中出现错误:', error);
    } finally {
        await browser.close();
    }
}

// 运行爬虫
scrapeQuestions().catch(console.error);
