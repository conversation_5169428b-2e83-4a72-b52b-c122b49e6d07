// ==UserScript==
// @name         CISP 自动答题 + 导出记录 (改进版)
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  自动遍历所有题目答题，记录题目/答案/解析并导出记录，支持已答/未答状态识别
// @match        https://www.cisp.cn/p/t_pc/pc_evaluation/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let records = [];
  let answering = false;
  let currentQuestionIndex = 0;
  let totalQuestions = 0;

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  function createFloatingUI() {
    const panel = document.createElement('div');
    panel.id = 'auto-answer-ui';
    panel.style.cssText = `
      position: fixed;
      top: 100px;
      right: 30px;
      background: #fff;
      border: 2px solid #4CAF50;
      border-radius: 10px;
      padding: 15px;
      z-index: 9999;
      box-shadow: 0 0 10px rgba(0,0,0,0.3);
      min-width: 220px;
    `;
    panel.innerHTML = `
      <div><strong>CISP自动答题助手</strong></div>
      <button id="startAutoAnswer" style="margin-top:10px;padding:5px 10px;width:100%">开始自动答题</button>
      <button id="stopAutoAnswer" style="margin-top:5px;padding:5px 10px;width:100%;display:none;background:#f44336;color:white">停止答题</button>
      <div id="answer-status" style="margin-top:10px;color:#555">状态：未启动</div>
      <div id="progress-info" style="margin-top:5px;color:#666;font-size:12px">进度：0/0</div>
      <div id="current-question" style="margin-top:5px;color:#333;font-size:11px">当前：未开始</div>
    `;
    document.body.appendChild(panel);

    document.getElementById('startAutoAnswer').onclick = () => {
      const ok = confirm("是否确认开始自动答题？\n系统将自动遍历所有题目、随机选择选项、记录并保存答案与解析。");
      if (ok) {
        document.getElementById('startAutoAnswer').style.display = 'none';
        document.getElementById('stopAutoAnswer').style.display = 'block';
        document.getElementById('answer-status').innerText = '状态：初始化中...';
        startAutoAnswering();
      }
    };

    document.getElementById('stopAutoAnswer').onclick = () => {
      answering = false;
      document.getElementById('answer-status').innerText = '状态：已停止';
      document.getElementById('startAutoAnswer').style.display = 'block';
      document.getElementById('stopAutoAnswer').style.display = 'none';
      if (records.length > 0) {
        exportTxt();
      }
    };
  }

  // 获取左侧题目列表 - 针对CISP网站优化
  function getQuestionList() {
    // 根据截图，左侧应该是数字按钮 1,2,3...
    let questionButtons = [];

    // 尝试多种可能的选择器
    const selectors = [
      '.question-list button',
      '.question-nav button',
      '.sidebar button',
      '.nav-item',
      '.question-item',
      'button[class*="question"]',
      '.left-panel button',
      '.question-panel button'
    ];

    for (const selector of selectors) {
      questionButtons = Array.from(document.querySelectorAll(selector));
      if (questionButtons.length > 0) {
        // 过滤出数字按钮
        questionButtons = questionButtons.filter(btn => {
          const text = btn.textContent.trim();
          return /^\d+$/.test(text) && parseInt(text) > 0;
        });
        if (questionButtons.length > 0) break;
      }
    }

    // 如果还是没找到，尝试通过父容器查找
    if (questionButtons.length === 0) {
      const containers = document.querySelectorAll('.sidebar, .left-panel, .question-container, [class*="nav"]');
      for (const container of containers) {
        const buttons = container.querySelectorAll('button');
        const numButtons = Array.from(buttons).filter(btn => /^\d+$/.test(btn.textContent.trim()));
        if (numButtons.length > 0) {
          questionButtons = numButtons;
          break;
        }
      }
    }

    // 按数字排序
    questionButtons.sort((a, b) => {
      const numA = parseInt(a.textContent.trim());
      const numB = parseInt(b.textContent.trim());
      return numA - numB;
    });

    console.log(`找到 ${questionButtons.length} 个题目按钮`);
    return questionButtons;
  }

  // 检查题目是否已答 - 针对CISP网站优化
  function isQuestionAnswered(questionButton) {
    const classList = questionButton.classList;
    const text = questionButton.textContent.trim();

    // 检查常见的已答状态类名
    const hasAnsweredClass = classList.contains('answered') ||
                           classList.contains('completed') ||
                           classList.contains('done') ||
                           classList.contains('selected') ||
                           classList.contains('active') ||
                           classList.contains('finished');

    // 检查按钮样式
    const style = window.getComputedStyle(questionButton);
    const bgColor = style.backgroundColor;
    const color = style.color;

    // 检查是否有特殊的背景色（通常已答题目会有不同颜色）
    const hasSpecialBg = bgColor !== 'rgba(0, 0, 0, 0)' &&
                        bgColor !== 'transparent' &&
                        bgColor !== 'rgb(255, 255, 255)' &&
                        bgColor !== 'rgb(248, 249, 250)'; // 常见的默认背景色

    // 检查是否有特殊的文字颜色
    const hasSpecialColor = color !== 'rgb(0, 0, 0)' &&
                           color !== 'rgb(33, 37, 41)' && // 常见的默认文字色
                           color !== 'rgba(0, 0, 0, 0.87)';

    const isAnswered = hasAnsweredClass || hasSpecialBg || hasSpecialColor;

    if (isAnswered) {
      console.log(`题目 ${text} 检测为已答状态`);
    }

    return isAnswered;
  }

  // 点击指定题目
  async function clickQuestion(questionButton) {
    questionButton.click();
    await delay(1500); // 等待页面加载
  }

  // 获取当前题目数据 - 针对CISP网站优化
  function getCurrentQuestionData() {
    // 根据截图，题目文本在 .questionStem 中
    let qText = '';
    const questionSelectors = [
      '.questionStem',
      '.question-content',
      '.question-text',
      '.question-stem',
      '[class*="question"][class*="stem"]',
      '.problem-content'
    ];

    for (const selector of questionSelectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.trim()) {
        qText = element.innerText.trim();
        break;
      }
    }

    // 获取选项 - 根据截图，选项在 .answerItem 中
    let options = [];
    const optionSelectors = [
      '.answerItem',
      '.option-item',
      '.answer-option',
      '.choice-item',
      '[class*="answer"][class*="item"]'
    ];

    for (const selector of optionSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        options = Array.from(elements).map(el => el.innerText.trim()).filter(text => text.length > 0);
        if (options.length > 0) break;
      }
    }

    console.log(`获取题目: ${qText.substring(0, 50)}...`);
    console.log(`获取选项: ${options.length} 个`);

    return { qText, options };
  }

  // 获取用户已选答案 - 针对CISP网站优化
  function getUserAnswer() {
    const selectedSelectors = [
      '.answerItem.selected',
      '.answerItem.active',
      '.option-item.selected',
      '.answer-option.selected',
      '.choice-item.selected',
      '[class*="answer"][class*="selected"]',
      '[class*="option"][class*="active"]'
    ];

    for (const selector of selectedSelectors) {
      const selected = document.querySelector(selector);
      if (selected) {
        const answer = selected.innerText.trim();
        console.log(`检测到已选答案: ${answer}`);
        return answer;
      }
    }

    return '';
  }

  // 获取正确答案和解析 - 针对CISP网站优化
  function getCorrectAnswerAndExplanation() {
    let correctAnswer = '';
    let explanation = '';

    // 查找正确答案
    const correctSelectors = [
      '.isCorrect',
      '.correct',
      '.right-answer',
      '[class*="correct"]',
      '.answer-result .correct',
      '.result-correct'
    ];

    for (const selector of correctSelectors) {
      const correctEl = document.querySelector(selector);
      if (correctEl) {
        const text = correctEl.innerText;
        const match = text.match(/[A-D]/);
        if (match) {
          correctAnswer = match[0];
          break;
        }
      }
    }

    // 查找解析
    const explanationSelectors = [
      '.question-analysis',
      '.parse-info',
      '.explanation',
      '.analysis',
      '.answer-explanation',
      '[class*="analysis"]',
      '[class*="explanation"]'
    ];

    for (const selector of explanationSelectors) {
      const explanationEl = document.querySelector(selector);
      if (explanationEl) {
        let text = explanationEl.innerText.trim();
        // 移除"解析"、"分析"等标题
        text = text.replace(/^(解析|分析|说明)[:：]?\s*/, '');
        if (text.length > 0) {
          explanation = text;
          break;
        }
      }
    }

    console.log(`正确答案: ${correctAnswer}, 解析长度: ${explanation.length}`);

    return { correctAnswer, explanation };
  }

  // 随机选择选项 - 针对CISP网站优化
  async function selectRandomOption() {
    const optionSelectors = [
      '.answerItem',
      '.option-item',
      '.answer-option',
      '.choice-item'
    ];

    let options = [];
    for (const selector of optionSelectors) {
      options = document.querySelectorAll(selector);
      if (options.length > 0) break;
    }

    if (options.length === 0) {
      console.log('未找到可选择的选项');
      return false;
    }

    // 检查是否已经选择了选项
    const alreadySelected = document.querySelector('.answerItem.selected, .option-item.selected, .answer-option.selected');
    if (alreadySelected) {
      console.log('选项已被选择，跳过随机选择');
      return true;
    }

    const index = Math.floor(Math.random() * options.length);
    console.log(`随机选择选项 ${index + 1}/${options.length}`);
    options[index].click();
    await delay(800);
    return true;
  }

  // 提交答案（如果需要）
  async function submitAnswer() {
    const submitBtn = [...document.querySelectorAll('button')].find(btn => 
      btn.innerText.includes("提交") || btn.innerText.includes("确认") || btn.innerText.includes("下一题")
    );
    if (submitBtn && !submitBtn.disabled) {
      submitBtn.click();
      await delay(1000);
    }
  }

  // 更新UI状态
  function updateUI(status, progress, current) {
    document.getElementById('answer-status').innerText = status;
    document.getElementById('progress-info').innerText = progress;
    document.getElementById('current-question').innerText = current;
  }

  // 主要答题循环
  async function startAutoAnswering() {
    answering = true;
    records = [];
    
    try {
      // 获取题目列表
      const questionList = getQuestionList();
      totalQuestions = questionList.length;
      
      if (totalQuestions === 0) {
        updateUI('错误：未找到题目列表', '0/0', '请检查页面结构');
        return;
      }
      
      updateUI('状态：开始答题', `0/${totalQuestions}`, '准备中...');
      
      for (let i = 0; i < questionList.length && answering; i++) {
        currentQuestionIndex = i + 1;
        const questionButton = questionList[i];
        const questionNum = questionButton.textContent.trim();
        
        updateUI('状态：答题中', `${i}/${totalQuestions}`, `题目 ${questionNum}`);
        
        // 点击题目
        await clickQuestion(questionButton);
        
        // 获取题目信息
        const { qText, options } = getCurrentQuestionData();
        if (!qText || options.length === 0) {
          console.log(`题目 ${questionNum} 数据获取失败，跳过`);
          continue;
        }
        
        // 检查是否已答
        const isAnswered = isQuestionAnswered(questionButton);
        let yourAnswer = '';
        
        if (isAnswered) {
          // 如果已答，获取已选答案
          yourAnswer = getUserAnswer();
          updateUI('状态：答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} (已答)`);
        } else {
          // 如果未答，随机选择答案
          const selected = await selectRandomOption();
          if (selected) {
            await delay(1000);
            yourAnswer = getUserAnswer();
            await submitAnswer();
          }
          updateUI('状态：答题中', `${i + 1}/${totalQuestions}`, `题目 ${questionNum} (新答)`);
        }
        
        // 获取正确答案和解析
        await delay(500);
        const { correctAnswer, explanation } = getCorrectAnswerAndExplanation();
        
        // 记录数据
        records.push({
          index: currentQuestionIndex,
          questionNumber: questionNum,
          question: qText,
          options,
          yourAnswer,
          correctAnswer,
          explanation,
          wasAnswered: isAnswered
        });
        
        console.log(`完成题目 ${questionNum}, 总计 ${records.length} 题`);
        
        // 短暂延迟
        await delay(500);
      }
      
      // 完成所有题目
      updateUI('状态：完成', `${records.length}/${totalQuestions}`, '正在导出...');
      exportTxt();
      
    } catch (error) {
      console.error('答题过程出错:', error);
      updateUI('状态：出错', `${records.length}/${totalQuestions}`, error.message);
    } finally {
      answering = false;
      document.getElementById('startAutoAnswer').style.display = 'block';
      document.getElementById('stopAutoAnswer').style.display = 'none';
    }
  }

  // 导出记录
  function exportTxt() {
    if (records.length === 0) {
      alert('没有记录可导出');
      return;
    }
    
    const content = records.map((rec, i) => 
      `【题目 ${rec.questionNumber}】${rec.wasAnswered ? ' (原已答)' : ' (新答题)'}\n` +
      `${rec.question}\n` +
      `${rec.options.join('\n')}\n` +
      `你的答案：${rec.yourAnswer}\n` +
      `正确答案：${rec.correctAnswer}\n` +
      `解析：${rec.explanation}\n` +
      `${'='.repeat(50)}\n`
    ).join('\n');

    const summary = `\n\n【答题总结】\n` +
                   `总题数：${records.length}\n` +
                   `原已答：${records.filter(r => r.wasAnswered).length}\n` +
                   `新答题：${records.filter(r => !r.wasAnswered).length}\n` +
                   `导出时间：${new Date().toLocaleString()}\n`;

    const blob = new Blob([content + summary], { type: "text/plain;charset=utf-8" });
    const link = document.createElement("a");
    link.download = `CISP_答题记录_${new Date().toISOString().slice(0,10)}_${Date.now()}.txt`;
    link.href = URL.createObjectURL(blob);
    link.click();
    
    updateUI('状态：已完成', `${records.length}/${totalQuestions}`, '记录已导出');
  }

  // 页面加载完成后初始化
  window.addEventListener('load', () => {
    setTimeout(createFloatingUI, 1000);
  });

  // 如果页面已经加载完成
  if (document.readyState === 'complete') {
    setTimeout(createFloatingUI, 1000);
  }

})();
